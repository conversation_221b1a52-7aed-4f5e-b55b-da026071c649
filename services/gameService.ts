import {
    addDoc,
    arrayRemove,
    arrayUnion,
    collection,
    deleteDoc,
    doc,
    getDoc,
    getDocs,
    onSnapshot,
    query,
    serverTimestamp,
    updateDoc,
    where
} from 'firebase/firestore';
import { db } from '../config/firebase';

// Card definitions - mapping your card assets to IDs
export const CARD_DECK = [
  // Hearts
  { id: 'hearts_ace', suit: 'hearts', rank: 'ace', image: require('../assets/cards/hearts_ace.png') },
  { id: 'hearts_seven', suit: 'hearts', rank: 'seven', image: require('../assets/cards/hearts_seven.png') },
  { id: 'hearts_eight', suit: 'hearts', rank: 'eight', image: require('../assets/cards/hearts_eight.png') },
  { id: 'hearts_nine', suit: 'hearts', rank: 'nine', image: require('../assets/cards/hearts_nine.png') },
  { id: 'hearts_ten', suit: 'hearts', rank: 'ten', image: require('../assets/cards/hearts_ten.png') },
  { id: 'hearts_jack', suit: 'hearts', rank: 'jack', image: require('../assets/cards/hearts_jack.png') },
  { id: 'hearts_queen', suit: 'hearts', rank: 'queen', image: require('../assets/cards/hearts_queen.png') },
  { id: 'hearts_king', suit: 'hearts', rank: 'king', image: require('../assets/cards/hearts_king.png') },
  // Clubs
  { id: 'clubs_ace', suit: 'clubs', rank: 'ace', image: require('../assets/cards/clubs_ace.png') },
  { id: 'clubs_seven', suit: 'clubs', rank: 'seven', image: require('../assets/cards/clubs_seven.png') },
  { id: 'clubs_eight', suit: 'clubs', rank: 'eight', image: require('../assets/cards/clubs_eight.png') },
  { id: 'clubs_nine', suit: 'clubs', rank: 'nine', image: require('../assets/cards/clubs_nine.png') },
  { id: 'clubs_ten', suit: 'clubs', rank: 'ten', image: require('../assets/cards/clubs_ten.png') },
  { id: 'clubs_jack', suit: 'clubs', rank: 'jack', image: require('../assets/cards/clubs_jack.png') },
  { id: 'clubs_queen', suit: 'clubs', rank: 'queen', image: require('../assets/cards/clubs_queen.png') },
  { id: 'clubs_king', suit: 'clubs', rank: 'king', image: require('../assets/cards/clubs_king.png') },
  // Spades
  { id: 'spades_ace', suit: 'spades', rank: 'ace', image: require('../assets/cards/spades_ace.png') },
  { id: 'spades_seven', suit: 'spades', rank: 'seven', image: require('../assets/cards/spades_seven.png') },
  { id: 'spades_eight', suit: 'spades', rank: 'eight', image: require('../assets/cards/spades_eight.png') },
  { id: 'spades_nine', suit: 'spades', rank: 'nine', image: require('../assets/cards/spades_nine.png') },
  { id: 'spades_ten', suit: 'spades', rank: 'ten', image: require('../assets/cards/spades_ten.png') },
  { id: 'spades_jack', suit: 'spades', rank: 'jack', image: require('../assets/cards/spades_jack.png') },
  { id: 'spades_queen', suit: 'spades', rank: 'queen', image: require('../assets/cards/spades_queen.png') },
  { id: 'spades_king', suit: 'spades', rank: 'king', image: require('../assets/cards/spades_king.png') },
  // Diamonds
  { id: 'diamonds_ace', suit: 'diamonds', rank: 'ace', image: require('../assets/cards/diamonds_ace.png') },
  { id: 'diamonds_seven', suit: 'diamonds', rank: 'seven', image: require('../assets/cards/diamonds_seven.png') },
  { id: 'diamonds_eight', suit: 'diamonds', rank: 'eight', image: require('../assets/cards/diamonds_eight.png') },
  { id: 'diamonds_nine', suit: 'diamonds', rank: 'nine', image: require('../assets/cards/diamonds_nine.png') },
  { id: 'diamonds_ten', suit: 'diamonds', rank: 'ten', image: require('../assets/cards/diamonds_ten.png') },
  { id: 'diamonds_jack', suit: 'diamonds', rank: 'jack', image: require('../assets/cards/diamonds_jack.png') },
  { id: 'diamonds_queen', suit: 'diamonds', rank: 'queen', image: require('../assets/cards/diamonds_queen.png') },
  { id: 'diamonds_king', suit: 'diamonds', rank: 'king', image: require('../assets/cards/diamonds_king.png') },
];

// Game state interfaces
export interface Card {
  id: string;
  suit: string;
  rank: string;
  image?: any;
}

export interface Player {
  id: string;
  name: string;
  position: 'bottom' | 'right' | 'top' | 'left';
  cards: Card[];
  isHost: boolean;
  tricksWon?: number; // Track tricks won for scoring
  teamId?: 'team1' | 'team2'; // Partnership team assignment
}

// Partnership system interfaces
export interface Team {
  id: 'team1' | 'team2';
  name: string;
  playerIds: string[];
  score: number; // Legacy point-based score (kept for compatibility)
  tricksWon: number;
  roundScore: number; // Score for current round
  // Official 304 token-based scoring system
  tokens: number; // Number of tokens held by this team (starts at 11)
  consecutiveWins: number; // Track consecutive round wins for win streak display
  totalRoundsWon: number; // Total rounds won throughout the game
}

export interface Partnership {
  team1: Team;
  team2: Team;
}

// Scoring validation interfaces
export interface ScoreValidationResult {
  bidMade: boolean;
  bidderTeamScore: number;
  bidAmount: number;
  bidderTeam: {
    id: string;
    name: string;
  };
  exceedsBid: boolean;
  excessPoints: number;
}

export interface ScoringDecision {
  decision: 'next_round' | 'play_till_continue';
  timestamp: number;
  decidedBy: string; // Host player ID
}

// Official 304 token-based scoring interfaces
export interface TokenTransfer {
  fromTeam: 'team1' | 'team2';
  toTeam: 'team1' | 'team2';
  amount: number;
  reason: 'successful_bid' | 'failed_bid' | 'caps_bonus' | 'wrong_caps_penalty' | 'caps_failure_penalty';
  bidAmount?: number;
  bidType?: string;
}

export interface GameCompletionResult {
  isGameComplete: boolean;
  winnerTeam?: {
    id: 'team1' | 'team2';
    name: string;
    finalTokens: number;
  };
  loserTeam?: {
    id: 'team1' | 'team2';
    name: string;
    finalTokens: number;
  };
  gameEndReason: 'all_tokens_won' | 'opponent_eliminated' | 'manual_end';
  totalRoundsPlayed: number;
  gameDuration: number; // in milliseconds
}

export interface ScoringDialogState {
  visible: boolean;
  scoreValidation?: ScoreValidationResult;
  pendingDecision: boolean;
}

export interface PlayedCard {
  cardId: string;
  playerId: string;
  position: 'bottom' | 'right' | 'top' | 'left';
  timestamp: any;
  isFaceDown?: boolean; // For closed trump games when player can't follow suit
  isRevealed?: boolean; // Whether the card has been revealed by trump maker
}

export interface Bid {
  playerId: string;
  amount: number;
  isPassed: boolean;
  timestamp: number; // Unix timestamp in milliseconds
  bidRound: number; // Track which round of bidding this bid was made in
}

// Advanced Trump Game Types
export type TrumpGameType = 
  | 'open'           // Trump revealed immediately, all cards face up
  | 'closed'         // Trump hidden, face-down cards when can't follow suit
  | 'no_trump'       // No trump suit, only suit hierarchy matters
  | 'super_trump'    // All trump cards revealed at start, power doubled
  | 'hidden_trump'   // Trump suit unknown until first trump is played
  | 'maker_choice'   // Trump maker gets special privileges
  | 'blind_trump'    // Trump card face down until revealed by condition
  | 'progressive';   // Trump suit changes based on tricks won

// Trump Reveal Conditions
export type TrumpRevealCondition = 
  | 'immediate'      // Reveal immediately (open trump)
  | 'first_trick'    // Reveal after first trick
  | 'on_trump_play'  // Reveal when first trump is played  
  | 'maker_choice'   // Trump maker decides when to reveal
  | 'never'          // Never reveal (no trump games)
  | 'auto_reveal';   // Auto reveal based on game rules

// Trump Special Rules
export interface TrumpSpecialRules {
  canChangeTrump?: boolean;           // Can trump maker change trump suit mid-game
  trumpPowerMultiplier?: number;      // Multiplier for trump card power (default 1)
  canExchangeCards?: boolean;         // Can trump maker exchange cards
  faceDownAdvantage?: boolean;        // Trump maker sees face-down cards
  autoRevealOnTrump?: boolean;        // Auto reveal when trump is played
  restrictTrumpLead?: boolean;        // Restrict leading with trump
  trumpRoundBonus?: number;           // Bonus points for winning with trump
  maxTrumpReveals?: number;           // Maximum times trump can be revealed
}

// Enhanced 304 Special Rules for 250+ bids
export interface SpecialBidRules {
  is250Plus: boolean;                 // Whether this is a 250+ bid
  autoRevealAfterFirstTrick: boolean; // 304 rule: trump revealed after first trick for 250+
  exhaustedTrumpEnforced: boolean;    // 304 rule: must lead all remaining trumps when exhausted
  communicationRestricted: boolean;   // 304 rule: no verbal communication during play
  faceDownInFirstTrickOnly: boolean;  // 304 rule: face-down cards only in first trick for 250+
}

export interface GameRoom {
  id: string;
  hostId: string;
  players: Player[];
  gameState: 'waiting' | 'bidding' | 'eight_card_bidding_option' | 'trump_selection' | 'trump_type_selection' | 'in_progress' | 'scoring' | 'finished';
  currentTurn: string; // player ID whose turn it is
  turnOrder: string[]; // array of player IDs in turn order
  playedCards: PlayedCard[];
  round: number;
  maxPlayers: number;
  trumpSuit?: string; // Trump suit for the current round
  trumpCard?: Card; // The selected trump indicator card
  trumpIndicatorCardId?: string; // Track the specific trump indicator card ID
  trumpGameType?: TrumpGameType; // Advanced trump game type
  trumpSpecialRules?: TrumpSpecialRules; // Special rules for this trump game
  trumpRevealCondition?: TrumpRevealCondition; // When trump should be revealed
  trickLeader?: string; // Player who leads the current trick
  lastTrickWinner?: string; // Winner of the last trick
  isTrumpRevealed?: boolean; // Whether trump has been revealed
  isTrumpSuitAnnounced?: boolean; // 304 rule: trump suit should not be announced
  canInspectFaceDown?: boolean; // Whether trump maker can inspect face-down cards
  trumpRevealCount?: number; // Number of times trump has been revealed
  trumpHistory?: string[]; // History of trump changes/reveals
  originalTrumpSuit?: string; // Original trump suit (for progressive trump)
  alternativeTrumpSuits?: string[]; // Alternative trump suits (for progressive trump)
  // 304 Card dealing phases
  hasDistributedRemainingCards?: boolean; // Track if remaining 4 cards have been distributed
  // Bidding system
  bids: Bid[]; // All bids made during bidding phase
  currentBid: number; // Current highest bid
  highestBidder?: string; // Player with highest bid
  biddingTurn: string; // Current player's turn to bid
  biddingRound: number; // Track bidding rounds
  biddingType?: BiddingType; // Type of bidding (four_card or eight_card)
  biddingRestartCount?: number; // Track how many times bidding has been restarted for this deal
  biddingContext?: BiddingContext; // Enhanced bidding context for 304 rules
  fourCardBids?: Bid[]; // Store four card bids separately for eight card phase
  eightCardBids?: Bid[]; // Store eight card bids separately
  askPartnerRequests?: AskPartnerRequest[]; // Track ask partner requests
  cardRejectionRequests?: { playerId: string; timestamp: number; }[]; // Track card rejections
  // Partnership system
  partnership?: Partnership; // Team assignments and scores
  gameScore: number; // Target score to win the game (typically 150-300 points)
  // Official 304 game completion tracking
  gameCompletion?: GameCompletionResult; // Track game completion state
  tokenTransferHistory?: TokenTransfer[]; // History of all token transfers
  // Scoring validation system
  scoringDialogState?: ScoringDialogState;
  lastScoringDecision?: ScoringDecision;
  createdAt: any;
  updatedAt: any;
}

// 304 Card point values according to the official rules
const CARD_POINTS: Record<string, number> = {
  'jack': 30,
  'nine': 20,
  'ace': 11,
  'ten': 10,
  'king': 3,
  'queen': 2,
  'eight': 0,
  'seven': 0
};

// Official 304 token-based scoring rules from https://www.pagat.com/jass/304.html
const TOKEN_RULES = {
  INITIAL_TOKENS_PER_TEAM: 11,
  TOTAL_TOKENS: 22,

  // Token distribution based on bid outcomes
  BID_LESS_THAN_200: {
    SUCCESS: 1, // win 1 token
    FAILURE: 2  // lose 2 tokens
  },
  BID_200_TO_249: {
    SUCCESS: 2, // win 2 tokens
    FAILURE: 3  // lose 3 tokens
  },
  BID_250_OR_MORE: {
    SUCCESS: 3, // win 3 tokens
    FAILURE: 4  // lose 4 tokens
  },
  PARTNER_CLOSE_CAPS: {
    SUCCESS: 4, // win 4 tokens
    FAILURE: 5  // lose 5 tokens
  },

  // Special conditions
  CAPS_BONUS: 1, // 1 extra token for correct Caps announcement (before 7th trick)
  WRONG_CAPS_PENALTY: 2, // lose 2 tokens for Wrong Caps
  CAPS_FAILURE_PENALTY: 5, // lose 5 tokens for losing trick after Caps announced

  // External Caps (opponents announce Caps)
  EXTERNAL_CAPS_BONUS: 1 // 1 token more than normal failed bid
};

// 304 Bidding types (for future enhancement)
export type BiddingType = 'four_card' | 'eight_card';

export interface BiddingRules {
  type: BiddingType;
  allowsEightCardSelection: boolean; // If true, trump can be selected from all 8 cards
  description: string;
}

// Card ranking for 304 (highest to lowest)
const CARD_RANKING: Record<string, number> = {
  'jack': 8,
  'nine': 7,
  'ace': 6,
  'ten': 5,
  'king': 4,
  'queen': 3,
  'eight': 2,
  'seven': 1
};

// Utility function to get card points
export const getCardPoints = (rank: string): number => {
  return CARD_POINTS[rank] || 0;
};

// Utility function to get card ranking value
export const getCardRanking = (rank: string): number => {
  return CARD_RANKING[rank] || 0;
};

// Partnership utility functions
export const createPartnership = (players: Player[]): Partnership => {
  // In 304, partners sit opposite each other
  // bottom & top are partners (team1), right & left are partners (team2)
  const bottomPlayer = players.find(p => p.position === 'bottom');
  const topPlayer = players.find(p => p.position === 'top');
  const rightPlayer = players.find(p => p.position === 'right');
  const leftPlayer = players.find(p => p.position === 'left');

  const team1PlayerIds = [bottomPlayer?.id, topPlayer?.id].filter(Boolean) as string[];
  const team2PlayerIds = [rightPlayer?.id, leftPlayer?.id].filter(Boolean) as string[];

  return {
    team1: {
      id: 'team1',
      name: 'North-South',
      playerIds: team1PlayerIds,
      score: 0,
      tricksWon: 0,
      roundScore: 0,
      // Official 304 token-based scoring initialization
      tokens: TOKEN_RULES.INITIAL_TOKENS_PER_TEAM,
      consecutiveWins: 0,
      totalRoundsWon: 0
    },
    team2: {
      id: 'team2',
      name: 'East-West',
      playerIds: team2PlayerIds,
      score: 0,
      tricksWon: 0,
      roundScore: 0,
      // Official 304 token-based scoring initialization
      tokens: TOKEN_RULES.INITIAL_TOKENS_PER_TEAM,
      consecutiveWins: 0,
      totalRoundsWon: 0
    }
  };
};

export const getPlayerTeam = (playerId: string, partnership: Partnership): Team | null => {
  if (partnership.team1.playerIds.includes(playerId)) {
    return partnership.team1;
  }
  if (partnership.team2.playerIds.includes(playerId)) {
    return partnership.team2;
  }
  return null;
};

export const getPartnerPlayer = (playerId: string, players: Player[]): Player | null => {
  const currentPlayer = players.find(p => p.id === playerId);
  if (!currentPlayer) return null;

  // Find partner based on opposite position
  const partnerPosition = {
    'bottom': 'top',
    'top': 'bottom', 
    'right': 'left',
    'left': 'right'
  }[currentPlayer.position];

  return players.find(p => p.position === partnerPosition) || null;
};

export const assignTeamIds = (players: Player[]): Player[] => {
  return players.map(player => {
    const teamId = (player.position === 'bottom' || player.position === 'top') ? 'team1' : 'team2';
    return { ...player, teamId };
  });
};

// Enhanced Bidding Validation and Logic - 304 Official Rules
export const BIDDING_RULES = {
  MIN_BID: 160, // 304 rule: minimum bid is half of total points (304/2 = 152, rounded to 160)
  MAX_BID: 300, // 304 rule: theoretical maximum (304 total points minus some margin)
  BID_INCREMENT: 10, // 304 rule: bids must be in multiples of 10
  SPECIAL_BID_THRESHOLD: 200, // 304 rule: 200+ bids have special rules
  HIGH_BID_THRESHOLD: 250, // 304 rule: 250+ bids have very special rules
  MAX_CONSECUTIVE_PASSES: 3, // Maximum consecutive passes to end bidding
  BIDDING_TIMEOUT_SECONDS: 30, // Time limit per bid (optional)
  EIGHT_CARD_MIN_BID: 250, // 304 rule: eight card bidding minimum
  CARD_REJECTION_THRESHOLD: 15, // 304 rule: can reject if total card value < 15
};

// 304 Card Rejection Rule - Check if player can reject their cards
export const canRejectCards = (cards: Card[]): boolean => {
  const totalValue = cards.reduce((sum, card) => sum + getCardPoints(card.rank), 0);
  return totalValue < BIDDING_RULES.CARD_REJECTION_THRESHOLD;
};

// 304 Bidding Types with proper rules
export type BiddingPhase = 'four_card' | 'eight_card';

export interface BiddingContext {
  phase: BiddingPhase;
  currentBid: number;
  fourCardBid?: number; // The winning four card bid
  fourCardBidder?: string; // Who won the four card bid
  hasFourCardBidCompleted: boolean;
}

// Validate if a bid is legal according to 304 rules
export const validateBid = (
  bidAmount: number, 
  currentBid: number, 
  playerBids: Bid[], 
  gameRoom: GameRoom,
  playerId?: string,
  biddingContext?: BiddingContext
): { isValid: boolean; error?: string } => {
  // Determine minimum bid based on bidding phase
  let minBid = BIDDING_RULES.MIN_BID;
  
  if (biddingContext?.phase === 'eight_card') {
    // 304 rule: eight card minimum is either 250 or current four card bid, whichever is higher
    minBid = Math.max(BIDDING_RULES.EIGHT_CARD_MIN_BID, biddingContext.fourCardBid || 0);
  }

  // Check minimum bid
  if (bidAmount < minBid) {
    return { isValid: false, error: `Minimum bid is ${minBid}` };
  }

  // Check maximum bid
  if (bidAmount > BIDDING_RULES.MAX_BID) {
    return { isValid: false, error: `Maximum bid is ${BIDDING_RULES.MAX_BID}` };
  }

  // 304 rule: bids must be in multiples of 10
  if (bidAmount % BIDDING_RULES.BID_INCREMENT !== 0) {
    return { isValid: false, error: `Bids must be in multiples of ${BIDDING_RULES.BID_INCREMENT}` };
  }

  // Check if bid is higher than current bid
  if (bidAmount <= currentBid) {
    return { isValid: false, error: `Bid must be higher than ${currentBid}` };
  }

  // 304 rule: Check for special bid rules (200+) - only for four card bidding
  if (playerId && gameRoom.partnership && biddingContext?.phase !== 'eight_card') {
    const partnerPlayer = getPartnerPlayer(playerId, gameRoom.players);
    const partnerBids = playerBids.filter(bid => bid.playerId === partnerPlayer?.id && !bid.isPassed);
    const playerOwnBids = playerBids.filter(bid => bid.playerId === playerId && !bid.isPassed);
    
    // Check if player has already bid (can only bid again if bidding 200+)
    if (playerOwnBids.length > 0 && bidAmount < BIDDING_RULES.SPECIAL_BID_THRESHOLD) {
      return { isValid: false, error: 'You can only make a second bid if bidding 200 or higher' };
    }

    // Check if overbidding partner (only allowed if someone else has bid 200+ or you're bidding 200+)
    if (partnerBids.length > 0) {
      const partnerHighestBid = Math.max(...partnerBids.map(bid => bid.amount));
      if (bidAmount > partnerHighestBid) {
        const hasSomeoneElseBid200Plus = gameRoom.bids.some(bid => 
          bid.amount >= BIDDING_RULES.SPECIAL_BID_THRESHOLD && 
          bid.playerId !== playerId && 
          bid.playerId !== partnerPlayer?.id &&
          !bid.isPassed
        );
        
        if (!hasSomeoneElseBid200Plus && bidAmount < BIDDING_RULES.SPECIAL_BID_THRESHOLD) {
          return { isValid: false, error: 'Cannot overbid partner unless someone else has bid 200+ or you bid 200+' };
        }
      }
    }
  }

  // 304 rule: Eight card bidding restrictions
  if (biddingContext?.phase === 'eight_card' && playerId && gameRoom.partnership) {
    const partnerPlayer = getPartnerPlayer(playerId, gameRoom.players);
    const partnerBids = playerBids.filter(bid => bid.playerId === partnerPlayer?.id && !bid.isPassed);
    const playerOwnBids = playerBids.filter(bid => bid.playerId === playerId && !bid.isPassed);
    
    // Cannot overbid partner in eight card bidding
    if (partnerBids.length > 0) {
      const partnerHighestBid = Math.max(...partnerBids.map(bid => bid.amount));
      if (bidAmount > partnerHighestBid) {
        return { isValid: false, error: 'Cannot overbid partner in eight card bidding' };
      }
    }
    
    // Cannot bid more than once in eight card bidding
    if (playerOwnBids.length > 0) {
      return { isValid: false, error: 'You can only bid once in eight card phase' };
    }
  }

  return { isValid: true };
};

// Check if bidding should end according to 304 rules
export const shouldEndBidding = (
  bids: Bid[], 
  currentBid: number, 
  highestBidder?: string,
  biddingContext?: BiddingContext
): boolean => {
  if (!highestBidder) {
    return false; // No valid bid yet
  }

  const minBid = biddingContext?.phase === 'eight_card' 
    ? Math.max(BIDDING_RULES.EIGHT_CARD_MIN_BID, biddingContext.fourCardBid || 0)
    : BIDDING_RULES.MIN_BID;

  if (currentBid < minBid) {
    return false; // No valid bid yet
  }

  // Check for 3 consecutive passes after a valid bid
  if (bids.length >= 3) {
    const lastThreeBids = bids.slice(-3);
    return lastThreeBids.every(bid => bid.isPassed);
  }

  return false;
};

// 304 Ask Partner Functionality
export interface AskPartnerRequest {
  requesterId: string;
  partnerId: string;
  timestamp: number;
  isProcessed: boolean;
}

// Check if player can ask partner to bid
export const canAskPartner = (
  playerId: string, 
  gameRoom: GameRoom, 
  biddingContext?: BiddingContext
): { canAsk: boolean; reason?: string; partnerId?: string } => {
  // 304 rule: Any player may ask his partner to bid in his turn, providing he himself is not bidding in that turn
  if (gameRoom.biddingTurn === playerId) {
    return { canAsk: false, reason: 'Cannot ask partner when it\'s your turn to bid' };
  }

  const partnerPlayer = getPartnerPlayer(playerId, gameRoom.players);
  if (!partnerPlayer) {
    return { canAsk: false, reason: 'Partner not found' };
  }

  if (gameRoom.biddingTurn !== partnerPlayer.id) {
    return { canAsk: false, reason: 'Can only ask partner when it\'s their turn to bid' };
  }

  return { canAsk: true, partnerId: partnerPlayer.id };
};

// Enhanced bidding direction - counter-clockwise starting from dealer's right
export const getBiddingStartPlayer = (turnOrder: string[], dealerId: string): string => {
  // 304 rule: Bidding starts with the person to the immediate right of the dealer
  // In counter-clockwise order, "right" means the previous player in turn order
  const dealerIndex = turnOrder.indexOf(dealerId);
  const startIndex = (dealerIndex - 1 + turnOrder.length) % turnOrder.length;
  return turnOrder[startIndex];
};

// Format bid amount for display (304 convention: drop the "1" from 160+)
export const formatBidDisplay = (bidAmount: number): string => {
  if (bidAmount >= 160 && bidAmount < 200) {
    // Drop the "1" - 160 becomes "60", 170 becomes "70", etc.
    return (bidAmount - 100).toString();
  }
  return bidAmount.toString();
};

// Parse display bid back to actual amount
export const parseBidDisplay = (displayBid: string): number => {
  const num = parseInt(displayBid);
  if (num >= 60 && num < 100) {
    // Add back the "1" - "60" becomes 160, "70" becomes 170, etc.
    return num + 100;
  }
  return num;
};

// Get bidding history summary
export const getBiddingHistory = (bids: Bid[], players: Player[]): string[] => {
  return bids.map(bid => {
    const player = players.find(p => p.id === bid.playerId);
    const playerName = player?.name || 'Unknown';
    
    if (bid.isPassed) {
      return `${playerName}: Pass`;
    } else {
      return `${playerName}: ${bid.amount}`;
    }
  });
};

// Calculate next bidder in counter-clockwise order
export const getNextBidder = (currentBidder: string, turnOrder: string[]): string => {
  const currentIndex = turnOrder.indexOf(currentBidder);
  const nextIndex = (currentIndex + 1) % turnOrder.length;
  return turnOrder[nextIndex];
};

// Check if all players have passed (special case)
export const allPlayersPassedInFirstRound = (bids: Bid[], playerCount: number): boolean => {
  if (bids.length !== playerCount) return false;
  return bids.every(bid => bid.isPassed);
};

// Check if all players have passed in the current bidding round (enhanced)
export const allPlayersPassedInCurrentRound = (bids: Bid[], playerCount: number, currentBiddingRound: number): boolean => {
  // Get bids from current round only
  const currentRoundBids = bids.filter(bid => bid.bidRound === currentBiddingRound);
  if (currentRoundBids.length !== playerCount) return false;
  return currentRoundBids.every(bid => bid.isPassed);
};

// Format timestamp for display
export const formatBidTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Advanced Trump Mechanics Utilities

// Get trump special rules based on game type
export const getTrumpSpecialRules = (gameType: TrumpGameType): TrumpSpecialRules => {
  const baseRules: TrumpSpecialRules = {
    trumpPowerMultiplier: 1,
    canChangeTrump: false,
    canExchangeCards: false,
    faceDownAdvantage: false,
    autoRevealOnTrump: false,
    restrictTrumpLead: false,
    trumpRoundBonus: 0,
    maxTrumpReveals: 1,
  };

  switch (gameType) {
    case 'open':
      return {
        ...baseRules,
        autoRevealOnTrump: true,
      };
    
    case 'closed':
      return {
        ...baseRules,
        faceDownAdvantage: true,
      };
    
    case 'no_trump':
      return {
        ...baseRules,
        trumpPowerMultiplier: 0, // No trump power
        maxTrumpReveals: 0,
      };
    
    case 'super_trump':
      return {
        ...baseRules,
        trumpPowerMultiplier: 2,
        autoRevealOnTrump: true,
        trumpRoundBonus: 5,
      };
    
    case 'hidden_trump':
      return {
        ...baseRules,
        autoRevealOnTrump: true,
        faceDownAdvantage: true,
      };
    
    case 'maker_choice':
      return {
        ...baseRules,
        canChangeTrump: true,
        canExchangeCards: true,
        faceDownAdvantage: true,
        maxTrumpReveals: 3,
      };
    
    case 'blind_trump':
      return {
        ...baseRules,
        faceDownAdvantage: true,
        restrictTrumpLead: true,
      };
    
    case 'progressive':
      return {
        ...baseRules,
        canChangeTrump: true,
        autoRevealOnTrump: true,
        trumpRoundBonus: 2,
        maxTrumpReveals: 4,
      };
    
    default:
      return baseRules;
  }
};

// Get trump reveal condition based on game type
export const getTrumpRevealCondition = (gameType: TrumpGameType): TrumpRevealCondition => {
  switch (gameType) {
    case 'open':
    case 'super_trump':
      return 'immediate';
    
    case 'closed':
    case 'maker_choice':
      return 'maker_choice';
    
    case 'hidden_trump':
    case 'progressive':
      return 'on_trump_play';
    
    case 'blind_trump':
      return 'first_trick';
    
    case 'no_trump':
      return 'never';
    
    default:
      return 'immediate';
  }
};

// Check if trump should be revealed based on condition
export const shouldRevealTrump = (
  condition: TrumpRevealCondition,
  gameRoom: GameRoom,
  context?: {
    trickNumber?: number;
    cardPlayed?: Card;
    playerId?: string;
  }
): boolean => {
  if (gameRoom.isTrumpRevealed) return false; // Already revealed

  switch (condition) {
    case 'immediate':
      return true;
    
    case 'first_trick':
      return (context?.trickNumber || 0) >= 1;
    
    case 'on_trump_play':
      return context?.cardPlayed?.suit === gameRoom.trumpSuit;
    
    case 'maker_choice':
      return context?.playerId === gameRoom.highestBidder;
    
    case 'never':
      return false;
    
    case 'auto_reveal':
      // Auto reveal based on game state
      return gameRoom.playedCards.length >= 4; // After first trick
    
    default:
      return false;
  }
};

// Calculate trump power multiplier
export const getTrumpPower = (
  card: Card,
  gameRoom: GameRoom,
  baseRanking: number
): number => {
  const rules = gameRoom.trumpSpecialRules || getTrumpSpecialRules(gameRoom.trumpGameType || 'open');
  
  if (card.suit === gameRoom.trumpSuit) {
    return baseRanking * (rules.trumpPowerMultiplier || 1);
  }
  
  return baseRanking;
};

// Get available trump suits for progressive trump
export const getProgressiveTrumpSuits = (originalTrumpSuit: string): string[] => {
  const allSuits = ['hearts', 'diamonds', 'clubs', 'spades'];
  return allSuits.filter(suit => suit !== originalTrumpSuit);
};

// Determine next trump suit in progressive trump
export const getNextProgressiveTrump = (
  currentTrump: string,
  tricksWon: number,
  alternativeSuits: string[]
): string => {
  if (alternativeSuits.length === 0) return currentTrump;
  
  // Change trump every 2 tricks won
  const changeIndex = Math.floor(tricksWon / 2) % alternativeSuits.length;
  return alternativeSuits[changeIndex];
};

// Validate trump change request
export const canChangeTrumpSuit = (
  gameRoom: GameRoom,
  playerId: string,
  newTrumpSuit: string
): { canChange: boolean; reason?: string } => {
  // Only trump maker can change trump
  if (gameRoom.highestBidder !== playerId) {
    return { canChange: false, reason: 'Only trump maker can change trump suit' };
  }

  // Check if trump type allows changing
  const specialRules = gameRoom.trumpSpecialRules || getTrumpSpecialRules(gameRoom.trumpGameType || 'open');
  if (!specialRules.canChangeTrump) {
    return { canChange: false, reason: 'This trump type does not allow changing trump suit' };
  }

  // Check maximum trump reveals
  if (specialRules.maxTrumpReveals && (gameRoom.trumpRevealCount || 0) >= specialRules.maxTrumpReveals) {
    return { canChange: false, reason: 'Maximum trump reveals reached' };
  }

  // Cannot change to the same suit
  if (newTrumpSuit === gameRoom.trumpSuit) {
    return { canChange: false, reason: 'Cannot change to the same trump suit' };
  }

  return { canChange: true };
};

// Trump Indicator Card Specific Rules for 304
export const canPlayTrumpIndicatorCard = (
  cardId: string,
  gameRoom: GameRoom,
  playerId: string,
  trickCards: PlayedCard[]
): { canPlay: boolean; reason?: string; mustPlayFaceDown?: boolean } => {
  // Check if this is the trump indicator card
  if (cardId !== gameRoom.trumpIndicatorCardId) {
    return { canPlay: true }; // Not trump indicator card, normal rules apply
  }

  // Only trump maker can play the trump indicator card
  if (gameRoom.highestBidder !== playerId) {
    return { canPlay: false, reason: 'Only trump maker can play the trump indicator card' };
  }

  // 304 RULE: When trump is revealed, standard trick-taking rules apply
  // Trump indicator card can be played normally like any other trump card
  if (gameRoom.isTrumpRevealed) {
    console.log('🎯 Server: Trump revealed - trump indicator card can be played normally');
    return { canPlay: true, mustPlayFaceDown: false };
  }

  // Below rules only apply when trump is NOT revealed (closed trump games)

  // Rule 1: Check if this is the eighth trick (last trick)
  const tricksCompleted = Math.floor(gameRoom.playedCards.length / gameRoom.players.length);
  const isEighthTrick = tricksCompleted === 7; // 0-indexed, so trick 7 is the 8th trick

  // Get trump maker's remaining cards
  const trumpMaker = gameRoom.players.find(p => p.id === playerId);
  if (!trumpMaker) {
    return { canPlay: false, reason: 'Trump maker not found' };
  }

  // Rule 2: In eighth trick, can only play if it's trump maker's only card
  if (isEighthTrick) {
    if (trumpMaker.cards.length === 1) {
      return { canPlay: true, mustPlayFaceDown: false }; // Can play face up in last trick
    } else {
      return { canPlay: false, reason: 'Trump indicator card can only be played in eighth trick if it\'s your only card' };
    }
  }

  // Rule 3: Can only be played face down to cut a non-trump trick led by another player
  const isFirstCardOfTrick = trickCards.length === 0;

  if (isFirstCardOfTrick) {
    // Trump maker is leading the trick - cannot play trump indicator card unless it's the last trick
    return { canPlay: false, reason: 'Trump indicator card cannot be led except in the eighth trick' };
  }

  // Check if the led suit is trump or non-trump
  const firstCard = trickCards[0];
  const firstCardData = getCardById(firstCard.cardId);
  const isTrumpLed = firstCardData.suit === gameRoom.trumpSuit;

  if (isTrumpLed) {
    // Trump was led - trump indicator card cannot be used to cut a trump trick in closed games
    return { canPlay: false, reason: 'Trump indicator card cannot be played to cut a trump trick' };
  }

  // Check if trump maker can follow suit
  const ledSuit = firstCardData.suit;
  const trumpMakerCanFollowSuit = trumpMaker.cards.some(card =>
    card.id !== cardId && card.suit === ledSuit
  );

  if (trumpMakerCanFollowSuit) {
    // Trump maker can follow suit with other cards - cannot use trump indicator card
    return { canPlay: false, reason: 'Must follow suit when possible. Trump indicator card can only be played when unable to follow suit' };
  }

  // All conditions met - can play trump indicator card face down to cut the non-trump trick
  return { canPlay: true, mustPlayFaceDown: true };
};

// Helper to check if trump maker has any non-trump cards when trump indicator is their only trump
export const isTrumpIndicatorOnlyTrump = (
  gameRoom: GameRoom,
  playerId: string
): boolean => {
  if (gameRoom.highestBidder !== playerId || !gameRoom.trumpIndicatorCardId) {
    return false;
  }

  const trumpMaker = gameRoom.players.find(p => p.id === playerId);
  if (!trumpMaker) return false;

  // Count trump cards in hand (excluding trump indicator card)
  const trumpCardsInHand = trumpMaker.cards.filter(card => 
    card.suit === gameRoom.trumpSuit && card.id !== gameRoom.trumpIndicatorCardId
  );

  // Trump indicator is only trump if no other trump cards exist
  return trumpCardsInHand.length === 0;
};

// Enhanced validation for trump indicator card - handles both closed and revealed trump scenarios
export const validateTrumpIndicatorInClosedGame = (
  cardId: string,
  gameRoom: GameRoom,
  playerId: string,
  trickCards: PlayedCard[]
): { isValid: boolean; message?: string; mustPlayFaceDown?: boolean } => {
  // Only validate trump indicator card
  if (cardId !== gameRoom.trumpIndicatorCardId) {
    return { isValid: true };
  }

  // 304 RULE: When trump is revealed, trump indicator card follows standard rules
  if (gameRoom.isTrumpRevealed) {
    console.log('🎯 Server: Trump revealed - trump indicator card validation bypassed');
    return { isValid: true, mustPlayFaceDown: false };
  }

  // Only apply special restrictions in closed trump games when trump is not revealed
  if (gameRoom.trumpGameType !== 'closed') {
    return { isValid: true };
  }

  const validation = canPlayTrumpIndicatorCard(cardId, gameRoom, playerId, trickCards);

  if (!validation.canPlay) {
    return { isValid: false, message: validation.reason };
  }

  // In closed trump games, provide additional context
  if (validation.mustPlayFaceDown) {
    return {
      isValid: true,
      mustPlayFaceDown: true,
      message: 'Trump indicator card will be played face down to cut this non-trump trick'
    };
  }

  return { isValid: true };
};

// 304 RULE: Validate trump card play when trump is revealed
export const validateTrumpRevealedCardPlay = (
  cardId: string,
  gameRoom: GameRoom,
  playerId: string,
  trickCards: PlayedCard[]
): { isValid: boolean; message?: string } => {
  // When trump is revealed, standard trick-taking rules apply
  if (gameRoom.isTrumpRevealed) {
    // All trump cards can be played normally when trump is revealed
    // Only trump indicator card has specific restrictions (handled elsewhere)
    return { isValid: true };
  }

  // If trump is not revealed, defer to other validation functions
  return { isValid: true };
};

// 304 RULE: Validate trump maker's card restrictions when unable to follow suit in closed trump games
export const validateTrumpMakerClosedGameRestrictions = (
  cardId: string,
  gameRoom: GameRoom,
  playerId: string,
  trickCards: PlayedCard[]
): { isValid: boolean; message?: string; mustPlayFaceDown?: boolean } => {
  // Only applies to trump maker in closed trump games when trump is NOT revealed
  if (gameRoom.trumpGameType !== 'closed' || gameRoom.highestBidder !== playerId || gameRoom.isTrumpRevealed) {
    return { isValid: true };
  }

  // Only applies when there are cards in the current trick (not leading)
  if (trickCards.length === 0) {
    return { isValid: true };
  }

  const trumpMaker = gameRoom.players.find(p => p.id === playerId);
  if (!trumpMaker) {
    return { isValid: false, message: 'Trump maker not found' };
  }

  // Get the led suit
  const firstCard = trickCards[0];
  const firstCardData = getCardById(firstCard.cardId);
  const ledSuit = firstCardData.suit;

  // Check if trump maker can follow suit
  const canFollowSuit = trumpMaker.cards.some(card => card.suit === ledSuit);

  // If trump maker can follow suit, normal rules apply
  if (canFollowSuit) {
    return { isValid: true };
  }

  // Trump maker cannot follow suit - apply 304 restrictions
  const cardToPlay = getCardById(cardId);
  const isTrumpIndicatorCard = cardId === gameRoom.trumpIndicatorCardId;

  // 304 RULE: When trump maker can't follow suit and trump not revealed:
  // 1. Can cut with trump indicator card (face down) - special restrictions apply
  // 2. Can play other trump cards normally (face down) - these are regular trump cards
  // 3. Can throw non-trump cards (face down)
  //
  // IMPORTANT: The restriction is only on the trump indicator card specifically,
  // not on all trump cards in the trump maker's hand

  // If it's the trump indicator card, validate it can be used to cut
  if (isTrumpIndicatorCard) {
    // Check if the led suit is trump (can't cut trump with trump indicator)
    if (ledSuit === gameRoom.trumpSuit) {
      return {
        isValid: false,
        message: 'Trump indicator card cannot be used to cut a trump trick'
      };
    }

    return {
      isValid: true,
      mustPlayFaceDown: true,
      message: 'Trump indicator card will be played face down to cut this non-trump trick'
    };
  }

  // Other trump cards or non-trump cards - can be played face down
  // According to 304 rules, trump maker can play any card when unable to follow suit
  return {
    isValid: true,
    mustPlayFaceDown: true,
    message: cardToPlay.suit === gameRoom.trumpSuit
      ? 'Trump card will be played face down as you cannot follow suit'
      : 'Non-trump card will be played face down as you cannot follow suit'
  };
};

// Helper function to determine if a card should be played face down
export const shouldPlayFaceDown = (
  playedCards: PlayedCard[],
  cardToPlay: Card,
  playerCards: Card[],
  isTrumpRevealed: boolean,
  trumpGameType?: TrumpGameType,
  gameRoom?: GameRoom
): boolean => {
  // If trump is revealed or it's an open trump game, all cards are played face up
  if (isTrumpRevealed || trumpGameType === 'open') return false;

  // If this is the first card of the trick, it's played face up (leading)
  const currentTrickSize = playedCards.length % 4; // Assuming 4 players
  if (currentTrickSize === 0) return false;

  // Get the suit of the leading card for this trick
  const currentTrickStart = Math.floor(playedCards.length / 4) * 4;
  const leadingCard = playedCards[currentTrickStart];
  if (!leadingCard) return false;

  const leadingSuit = getCardById(leadingCard.cardId).suit;

  // Check if player can follow suit
  let cardsOfLedSuit = playerCards.filter(card => getCardById(card.id).suit === leadingSuit);

  // 304 SPECIAL RULE: Trump indicator card cannot be used to cut trump trick
  // If trump suit is led in a closed trump game, exclude trump indicator card from follow suit check
  if (gameRoom &&
      trumpGameType === 'closed' &&
      !isTrumpRevealed &&
      leadingSuit === gameRoom.trumpSuit &&
      gameRoom.trumpIndicatorCardId) {

    cardsOfLedSuit = cardsOfLedSuit.filter(card => getCardById(card.id).id !== gameRoom.trumpIndicatorCardId);
    console.log('🎯 shouldPlayFaceDown: Trump led in closed game - excluding trump indicator from follow suit check');
    console.log('🎯 shouldPlayFaceDown: Effective cards of led suit after excluding trump indicator:', cardsOfLedSuit.length);
  }

  const canFollowSuit = cardsOfLedSuit.length > 0;

  // In closed trump game: if player cannot follow suit, they must play face down
  return trumpGameType === 'closed' && !canFollowSuit;
};

// Utility function to determine trick winner according to 304 rules
export const determineTrickWinner = (playedCards: PlayedCard[], trumpSuit?: string): string => {
  if (playedCards.length === 0) throw new Error('No cards played');
  
  console.log('🃏 Determining trick winner:');
  console.log('Trump suit:', trumpSuit);
  console.log('Played cards:', playedCards.map(pc => {
    const card = getCardById(pc.cardId);
    return `${card.suit} ${card.rank} (Player: ${pc.playerId})`;
  }));
  
  let winningCard = playedCards[0];
  let winningPlayer = playedCards[0].playerId;
  let winningCardData = getCardById(winningCard.cardId);
  
  console.log(`Initial winner: ${winningCardData.suit} ${winningCardData.rank} (${winningPlayer})`);
  
  for (let i = 1; i < playedCards.length; i++) {
    const currentCard = playedCards[i];
    const currentCardData = getCardById(currentCard.cardId);
    
    console.log(`Comparing: ${currentCardData.suit} ${currentCardData.rank} vs ${winningCardData.suit} ${winningCardData.rank}`);
    
    // Check if current card beats winning card
    if (isCardHigher(currentCardData, winningCardData, trumpSuit)) {
      console.log(`${currentCardData.suit} ${currentCardData.rank} beats ${winningCardData.suit} ${winningCardData.rank}!`);
      winningCard = currentCard;
      winningPlayer = currentCard.playerId;
      winningCardData = currentCardData;
    } else {
      console.log(`${winningCardData.suit} ${winningCardData.rank} still winning`);
    }
  }
  
  console.log(`🏆 Final winner: ${winningCardData.suit} ${winningCardData.rank} (${winningPlayer})`);
  return winningPlayer;
};

// Utility function to compare two cards according to 304 rules
const isCardHigher = (card1: Card, card2: Card, trumpSuit?: string): boolean => {
  console.log(`🔍 Comparing cards: ${card1.suit} ${card1.rank} vs ${card2.suit} ${card2.rank}`);
  console.log(`Trump suit: ${trumpSuit}`);
  
  // If one card is trump and other isn't, trump wins
  if (trumpSuit) {
    const card1IsTrump = card1.suit === trumpSuit;
    const card2IsTrump = card2.suit === trumpSuit;
    
    console.log(`Card1 is trump: ${card1IsTrump}, Card2 is trump: ${card2IsTrump}`);
    
    if (card1IsTrump && !card2IsTrump) {
      console.log('Card1 wins because it is trump');
      return true;
    }
    if (card2IsTrump && !card1IsTrump) {
      console.log('Card1 loses because card2 is trump');
      return false;
    }
  }
  
  // If both cards are trump or both non-trump, compare by ranking
  const rank1 = getCardRanking(card1.rank);
  const rank2 = getCardRanking(card2.rank);
  
  console.log(`Card1 ranking: ${rank1} (${card1.rank}), Card2 ranking: ${rank2} (${card2.rank})`);
  console.log(`Card1 > Card2: ${rank1 > rank2}`);
  
  return rank1 > rank2;
};

// Utility function to shuffle array
const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Utility function to get card by ID
export const getCardById = (cardId: string): Card => {
  const card = CARD_DECK.find(c => c.id === cardId);
  if (!card) throw new Error(`Card with ID ${cardId} not found`);
  return card;
};

// Utility function to distribute initial 4 cards to players
const distributeInitialCards = (players: Player[]): { players: Player[], remainingDeck: Card[] } => {
  const shuffledDeck = shuffleArray([...CARD_DECK]);
  const cardsPerPlayer = 4;
  const totalInitialCards = players.length * cardsPerPlayer;
  
  const playersWithCards = players.map((player, index) => ({
    ...player,
    cards: shuffledDeck.slice(index * cardsPerPlayer, (index + 1) * cardsPerPlayer)
  }));
  
  const remainingDeck = shuffledDeck.slice(totalInitialCards);
  
  return { players: playersWithCards, remainingDeck };
};

// Utility function to distribute remaining 4 cards to players after bidding
const distributeRemainingCards = (players: Player[], remainingDeck: Card[]): Player[] => {
  const cardsPerPlayer = 4;
  
  return players.map((player, index) => ({
    ...player,
    cards: [...player.cards, ...remainingDeck.slice(index * cardsPerPlayer, (index + 1) * cardsPerPlayer)]
  }));
};

// Helper function to recursively clean undefined values from objects before Firestore updates
const cleanFirestoreData = (data: any): any => {
  if (data === null || data === undefined) {
    return null;
  }
  
  if (Array.isArray(data)) {
    return data.map(cleanFirestoreData);
  }
  
  if (typeof data === 'object' && data !== null) {
    const cleaned: Record<string, any> = {};
    Object.keys(data).forEach(key => {
      const value = data[key];
      if (value !== undefined) {
        cleaned[key] = cleanFirestoreData(value);
      }
    });
    return cleaned;
  }
  
  return data;
};

// Enhanced Game Flow Control Interfaces
export interface RoundState {
  roundNumber: number;
  dealNumber: number; // Track multiple deals within a round
  phase: 'dealing' | 'bidding' | 'trump_selection' | 'trump_type_selection' | 'in_progress' | 'scoring' | 'finished';
  tricksCompleted: number;
  totalTricks: number;
  roundStartTime: number;
  phaseStartTime: number;
  timeouts: Record<string, number>; // Track timeouts for different phases
}

export interface GameFlow {
  currentRound: RoundState;
  gameProgress: {
    totalRounds: number;
    roundsCompleted: number;
    estimatedTimeRemaining: number;
    gameStartTime: number;
  };
  dealerRotation: {
    currentDealer: string;
    dealerHistory: string[];
    dealingOrder: string[];
  };
  phaseTransitions: {
    autoAdvance: boolean;
    timeouts: Record<string, number>;
    requiresPlayerAction: boolean;
    pendingActions: string[];
  };
}

export interface GameStateHistory {
  timestamp: number;
  phase: string;
  action: string;
  playerId?: string;
  details: any;
}

export interface EnhancedGameRoom extends GameRoom {
  gameFlow: GameFlow;
  stateHistory: GameStateHistory[];
  autoProgressSettings: {
    enableAutoProgress: boolean;
    bidTimeout: number; // seconds
    playTimeout: number; // seconds
    trumpSelectionTimeout: number; // seconds
  };
  dealProgression: {
    cardsPerDeal: number;
    totalDeals: number;
    currentDeal: number;
    dealPattern: 'standard' | 'progressive' | 'custom';
  };
}

export class GameService {

  // Enhanced Game Flow Control Methods
  
  // Initialize game flow state
  static initializeGameFlow(players: Player[], hostId: string): GameFlow {
    const now = Date.now();
    return {
      currentRound: {
        roundNumber: 1,
        dealNumber: 1,
        phase: 'dealing',
        tricksCompleted: 0,
        totalTricks: 8, // 304 standard
        roundStartTime: now,
        phaseStartTime: now,
        timeouts: {}
      },
      gameProgress: {
        totalRounds: 10, // Configurable
        roundsCompleted: 0,
        estimatedTimeRemaining: 0,
        gameStartTime: now
      },
      dealerRotation: {
        currentDealer: hostId,
        dealerHistory: [hostId],
        dealingOrder: players.map(p => p.id)
      },
      phaseTransitions: {
        autoAdvance: false,
        timeouts: {
          bidding: 30000, // 30 seconds
          playing: 15000, // 15 seconds
          trump_selection: 20000 // 20 seconds
        },
        requiresPlayerAction: true,
        pendingActions: []
      }
    };
  }

  // Log game state change
  static logGameStateChange(
    stateHistory: GameStateHistory[],
    phase: string,
    action: string,
    playerId?: string,
    details?: any
  ): GameStateHistory[] {
    const newEntry: GameStateHistory = {
      timestamp: Date.now(),
      phase,
      action,
      playerId,
      details: details || {}
    };
    
    // Keep last 100 entries to prevent memory bloat
    const updatedHistory = [...stateHistory, newEntry].slice(-100);
    return updatedHistory;
  }

  // Advance to next phase in game flow
  static async advanceGamePhase(roomId: string, newPhase: RoundState['phase'], triggeredBy?: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;
      const now = Date.now();
      
      // Update game flow
      const updatedGameFlow: GameFlow = {
        ...roomData.gameFlow,
        currentRound: {
          ...roomData.gameFlow.currentRound,
          phase: newPhase,
          phaseStartTime: now
        }
      };

      // Log the phase change
      const updatedHistory = this.logGameStateChange(
        roomData.stateHistory || [],
        newPhase,
        'phase_advance',
        triggeredBy,
        { previousPhase: roomData.gameFlow?.currentRound?.phase }
      );

      const updateData = {
        gameFlow: updatedGameFlow,
        stateHistory: updatedHistory,
        updatedAt: serverTimestamp()
      };

      await updateDoc(roomRef, cleanFirestoreData(updateData));
    } catch (error) {
      console.error('Error advancing game phase:', error);
      throw error;
    }
  }

  // Handle automatic deal progression
  static async progressDeal(roomId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;
      
      // Determine next dealer
      const currentDealerIndex = roomData.gameFlow.dealerRotation.dealingOrder.indexOf(
        roomData.gameFlow.dealerRotation.currentDealer
      );
      const nextDealerIndex = (currentDealerIndex + 1) % roomData.gameFlow.dealerRotation.dealingOrder.length;
      const nextDealer = roomData.gameFlow.dealerRotation.dealingOrder[nextDealerIndex];

      // Reset for next deal - Start fresh like a new game
      const { players: playersWithCards, remainingDeck } = distributeInitialCards(roomData.players);
      // Don't distribute remaining cards immediately - wait for bidding to complete

      // Update game flow for new deal
      const updatedGameFlow: GameFlow = {
        ...roomData.gameFlow,
        currentRound: {
          ...roomData.gameFlow.currentRound,
          dealNumber: roomData.gameFlow.currentRound.dealNumber + 1,
          phase: 'dealing',
          tricksCompleted: 0,
          roundStartTime: Date.now(),
          phaseStartTime: Date.now()
        },
        dealerRotation: {
          ...roomData.gameFlow.dealerRotation,
          currentDealer: nextDealer,
          dealerHistory: [...roomData.gameFlow.dealerRotation.dealerHistory, nextDealer]
        }
      };

      // Calculate the new round number (increment when dealNumber reaches a new cycle)
      const newRoundNumber = Math.floor((updatedGameFlow.currentRound.dealNumber - 1) / 4) + 1;
      updatedGameFlow.currentRound.roundNumber = newRoundNumber;

      // Log deal progression
      const updatedHistory = this.logGameStateChange(
        roomData.stateHistory || [],
        'dealing',
        'new_deal',
        nextDealer,
        { dealNumber: updatedGameFlow.currentRound.dealNumber }
      );

      const updateData = {
        players: playersWithCards,
        gameFlow: updatedGameFlow,
        stateHistory: updatedHistory,
        playedCards: [],
        currentTurn: nextDealer,
        trickLeader: nextDealer,
        gameState: 'bidding',
        round: updatedGameFlow.currentRound.roundNumber, // Sync legacy round field
        bids: [],
        currentBid: 150, // 304 rule: start just below minimum bid (160)
        biddingTurn: nextDealer,
        biddingRound: 1,
        biddingRestartCount: 0, // Reset restart count for new deal
        trumpSuit: null,
        trumpCard: null,
        trumpIndicatorCardId: null,
        trumpGameType: null,
        trumpSpecialRules: null,
        trumpRevealCondition: null,
        isTrumpRevealed: false,
        isTrumpSuitAnnounced: false,
        canInspectFaceDown: false,
        trumpRevealCount: 0,
        trumpHistory: [],
        originalTrumpSuit: null,
        alternativeTrumpSuits: null,
        hasDistributedRemainingCards: false,
        highestBidder: null,
        updatedAt: serverTimestamp()
      };

      await updateDoc(roomRef, cleanFirestoreData(updateData));

      // Store the remaining deck in a separate collection for the new deal
      await addDoc(collection(db, 'remainingDecks'), {
        roomId,
        cards: remainingDeck,
        createdAt: serverTimestamp()
      });

      // Clean up old remaining decks for this room
      const oldRemainingDecksQuery = collection(db, 'remainingDecks');
      const oldRemainingDecksSnapshot = await getDocs(query(oldRemainingDecksQuery, where('roomId', '==', roomId)));
      
      // Delete all but the most recent remaining deck entry
      const docs = oldRemainingDecksSnapshot.docs.sort((a, b) => 
        b.data().createdAt.toMillis() - a.data().createdAt.toMillis()
      );
      
      for (let i = 1; i < docs.length; i++) {
        await deleteDoc(docs[i].ref);
      }

      // Auto-advance to bidding phase
      setTimeout(() => {
        this.advanceGamePhase(roomId, 'bidding', nextDealer);
      }, 2000);

    } catch (error) {
      console.error('Error progressing deal:', error);
      throw error;
    }
  }

  // Handle round completion and scoring with validation
  static async completeRound(roomId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);

      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;

      // Validate scoring according to 304 rules
      const scoreValidation = this.validateScoring(roomData);

      if (!scoreValidation) {
        throw new Error('Unable to validate scoring');
      }

      // Check if bidder team exceeded their bid and host dialog is needed
      if (scoreValidation.exceedsBid) {
        // Show dialog to host for decision
        const scoringDialogState: ScoringDialogState = {
          visible: true,
          scoreValidation: {
            bidMade: scoreValidation.bidMade,
            bidderTeamScore: scoreValidation.bidderTeamScore,
            bidAmount: scoreValidation.bidAmount,
            bidderTeam: {
              id: scoreValidation.bidderTeam.id,
              name: scoreValidation.bidderTeam.name || 'Unknown Team'
            },
            exceedsBid: scoreValidation.exceedsBid,
            excessPoints: scoreValidation.excessPoints
          },
          pendingDecision: true
        };

        const updatedHistory = this.logGameStateChange(
          roomData.stateHistory || [],
          'scoring',
          'scoring_validation_dialog',
          'system',
          {
            bidMade: scoreValidation.bidMade,
            exceedsBid: scoreValidation.exceedsBid,
            excessPoints: scoreValidation.excessPoints
          }
        );

        const updateData = {
          gameState: 'scoring',
          scoringDialogState,
          stateHistory: updatedHistory,
          updatedAt: serverTimestamp()
        };

        await updateDoc(roomRef, cleanFirestoreData(updateData));

        return; // Wait for host decision
      }

      // No dialog needed, proceed with normal scoring
      await this.processScoringDecision(roomId, 'next_round', 'system');

    } catch (error) {
      console.error('Error completing round:', error);
      throw error;
    }
  }

  // Calculate scores based on 304 rules with detailed validation
  static calculateRoundScores(roomData: EnhancedGameRoom): { team1: number; team2: number } | null {
    if (!roomData.partnership || !roomData.highestBidder) {
      return null;
    }

    const bidAmount = roomData.currentBid;
    const highestBidder = roomData.highestBidder;
    const bidderTeam = getPlayerTeam(highestBidder, roomData.partnership);

    if (!bidderTeam) return null;

    const bidderTeamScore = this.calculateTeamTrickPoints(bidderTeam, roomData.players);
    const bidMade = bidderTeamScore >= bidAmount;

    let team1Score = 0;
    let team2Score = 0;

    if (bidMade) {
      // Bid successful - bidding team gets their points
      if (bidderTeam.id === 'team1') {
        team1Score = bidderTeamScore;
        team2Score = 304 - bidderTeamScore;
      } else {
        team2Score = bidderTeamScore;
        team1Score = 304 - bidderTeamScore;
      }
    } else {
      // Bid failed - opposing team gets points based on failure
      const failurePoints = Math.max(bidAmount, 160); // Minimum penalty
      if (bidderTeam.id === 'team1') {
        team1Score = -failurePoints;
        team2Score = 304;
      } else {
        team2Score = -failurePoints;
        team1Score = 304;
      }
    }

    return { team1: team1Score, team2: team2Score };
  }

  // Validate scoring according to 304 rules and determine if host dialog is needed
  static validateScoring(roomData: EnhancedGameRoom): ScoreValidationResult | null {
    if (!roomData.partnership || !roomData.highestBidder) {
      return null;
    }

    const bidAmount = roomData.currentBid;
    const highestBidder = roomData.highestBidder;
    const bidderTeam = getPlayerTeam(highestBidder, roomData.partnership);

    if (!bidderTeam) return null;

    const bidderTeamScore = this.calculateTeamTrickPoints(bidderTeam, roomData.players);
    const bidMade = bidderTeamScore >= bidAmount;
    const exceedsBid = bidMade && bidderTeamScore > bidAmount;
    const excessPoints = exceedsBid ? bidderTeamScore - bidAmount : 0;

    return {
      bidMade,
      bidderTeamScore,
      bidAmount,
      bidderTeam: {
        id: bidderTeam.id,
        name: bidderTeam.name || 'Unknown Team'
      },
      exceedsBid,
      excessPoints
    };
  }

  // Calculate team's total trick points based on actual cards won
  static calculateTeamTrickPoints(team: Team, players: Player[]): number {
    // Get all players in this team
    const teamPlayers = players.filter(p => p.teamId === team.id);

    // Calculate total points from cards won by team members
    let totalPoints = 0;
    teamPlayers.forEach(player => {
      if (player.tricksWon && player.tricksWon > 0) {
        // For now, use average points per trick until we implement detailed trick tracking
        // In 304, total points = 304, so average per trick = 304/8 = 38
        totalPoints += player.tricksWon * 38;
      }
    });

    return totalPoints;
  }

  // Check if game should end based on official 304 token rules
  static checkGameEndCondition(partnership?: Partnership): GameCompletionResult {
    const defaultResult: GameCompletionResult = {
      isGameComplete: false,
      gameEndReason: 'manual_end',
      totalRoundsPlayed: 0,
      gameDuration: 0
    };

    if (!partnership) return defaultResult;

    // Check if either team has all tokens (22) or the other team has 0 tokens
    const team1HasAllTokens = partnership.team1.tokens >= TOKEN_RULES.TOTAL_TOKENS;
    const team2HasAllTokens = partnership.team2.tokens >= TOKEN_RULES.TOTAL_TOKENS;
    const team1Eliminated = partnership.team1.tokens <= 0;
    const team2Eliminated = partnership.team2.tokens <= 0;

    if (team1HasAllTokens || team2Eliminated) {
      return {
        isGameComplete: true,
        winnerTeam: {
          id: 'team1',
          name: partnership.team1.name,
          finalTokens: partnership.team1.tokens
        },
        loserTeam: {
          id: 'team2',
          name: partnership.team2.name,
          finalTokens: partnership.team2.tokens
        },
        gameEndReason: team1HasAllTokens ? 'all_tokens_won' : 'opponent_eliminated',
        totalRoundsPlayed: partnership.team1.totalRoundsWon + partnership.team2.totalRoundsWon,
        gameDuration: Date.now() - (Date.now() - 3600000) // Placeholder - should use actual game start time
      };
    }

    if (team2HasAllTokens || team1Eliminated) {
      return {
        isGameComplete: true,
        winnerTeam: {
          id: 'team2',
          name: partnership.team2.name,
          finalTokens: partnership.team2.tokens
        },
        loserTeam: {
          id: 'team1',
          name: partnership.team1.name,
          finalTokens: partnership.team1.tokens
        },
        gameEndReason: team2HasAllTokens ? 'all_tokens_won' : 'opponent_eliminated',
        totalRoundsPlayed: partnership.team1.totalRoundsWon + partnership.team2.totalRoundsWon,
        gameDuration: Date.now() - (Date.now() - 3600000) // Placeholder - should use actual game start time
      };
    }

    return defaultResult;
  }

  // Calculate token transfer based on official 304 rules
  static calculateTokenTransfer(
    bidAmount: number,
    bidderTeamScore: number,
    bidType: string = 'standard',
    specialConditions?: {
      isCaps?: boolean;
      isWrongCaps?: boolean;
      isExternalCaps?: boolean;
      capsAnnouncedBeforeSeventhTrick?: boolean;
      lostTrickAfterCaps?: boolean;
    }
  ): { tokensToTransfer: number; reason: TokenTransfer['reason'] } {
    const bidMade = bidderTeamScore >= bidAmount;

    // Handle special conditions first
    if (specialConditions?.isWrongCaps) {
      return {
        tokensToTransfer: TOKEN_RULES.WRONG_CAPS_PENALTY,
        reason: 'wrong_caps_penalty'
      };
    }

    if (specialConditions?.lostTrickAfterCaps) {
      return {
        tokensToTransfer: TOKEN_RULES.CAPS_FAILURE_PENALTY,
        reason: 'caps_failure_penalty'
      };
    }

    // Determine base token transfer based on bid amount
    let baseTokens: number;

    if (bidType === 'partner_close_caps') {
      baseTokens = bidMade ? TOKEN_RULES.PARTNER_CLOSE_CAPS.SUCCESS : TOKEN_RULES.PARTNER_CLOSE_CAPS.FAILURE;
    } else if (bidAmount >= 250) {
      baseTokens = bidMade ? TOKEN_RULES.BID_250_OR_MORE.SUCCESS : TOKEN_RULES.BID_250_OR_MORE.FAILURE;
    } else if (bidAmount >= 200) {
      baseTokens = bidMade ? TOKEN_RULES.BID_200_TO_249.SUCCESS : TOKEN_RULES.BID_200_TO_249.FAILURE;
    } else {
      baseTokens = bidMade ? TOKEN_RULES.BID_LESS_THAN_200.SUCCESS : TOKEN_RULES.BID_LESS_THAN_200.FAILURE;
    }

    // Add Caps bonus if applicable
    if (bidMade && specialConditions?.isCaps && specialConditions?.capsAnnouncedBeforeSeventhTrick) {
      baseTokens += TOKEN_RULES.CAPS_BONUS;
    }

    // Add External Caps bonus if applicable
    if (!bidMade && specialConditions?.isExternalCaps) {
      baseTokens += TOKEN_RULES.EXTERNAL_CAPS_BONUS;
    }

    return {
      tokensToTransfer: baseTokens,
      reason: bidMade ? 'successful_bid' : 'failed_bid'
    };
  }

  // Process scoring decision from host with official 304 token-based scoring
  static async processScoringDecision(
    roomId: string,
    decision: 'next_round' | 'play_till_continue',
    decidedBy: string
  ): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);

      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;

      if (!roomData.partnership || !roomData.highestBidder) {
        throw new Error('Missing partnership or bidder information');
      }

      // Calculate token transfer based on official 304 rules
      const bidAmount = roomData.currentBid;
      const bidderTeam = getPlayerTeam(roomData.highestBidder, roomData.partnership);

      if (!bidderTeam) {
        throw new Error('Unable to determine bidder team');
      }

      const bidderTeamScore = this.calculateTeamTrickPoints(bidderTeam, roomData.players);

      // Calculate token transfer (for now, using basic rules - can be enhanced with special conditions)
      const tokenTransfer = this.calculateTokenTransfer(bidAmount, bidderTeamScore);

      // Determine which team wins/loses tokens
      const bidMade = bidderTeamScore >= bidAmount;
      const winningTeamId = bidMade ? bidderTeam.id : (bidderTeam.id === 'team1' ? 'team2' : 'team1');
      const losingTeamId = bidMade ? (bidderTeam.id === 'team1' ? 'team2' : 'team1') : bidderTeam.id;

      // Update partnership with token transfers and win streaks
      let updatedPartnership = { ...roomData.partnership };

      // Transfer tokens
      const tokensToTransfer = tokenTransfer.tokensToTransfer;
      updatedPartnership[winningTeamId].tokens = Math.min(
        TOKEN_RULES.TOTAL_TOKENS,
        updatedPartnership[winningTeamId].tokens + tokensToTransfer
      );
      updatedPartnership[losingTeamId].tokens = Math.max(
        0,
        updatedPartnership[losingTeamId].tokens - tokensToTransfer
      );

      // Update win streaks and round counts
      if (bidMade) {
        // Bidder team won
        updatedPartnership[bidderTeam.id].consecutiveWins += 1;
        updatedPartnership[bidderTeam.id].totalRoundsWon += 1;
        // Reset opponent's win streak
        const opponentTeamId = bidderTeam.id === 'team1' ? 'team2' : 'team1';
        updatedPartnership[opponentTeamId].consecutiveWins = 0;
      } else {
        // Opponent team won
        const opponentTeamId = bidderTeam.id === 'team1' ? 'team2' : 'team1';
        updatedPartnership[opponentTeamId].consecutiveWins += 1;
        updatedPartnership[opponentTeamId].totalRoundsWon += 1;
        // Reset bidder team's win streak
        updatedPartnership[bidderTeam.id].consecutiveWins = 0;
      }

      // Calculate legacy round scores for compatibility
      const roundScores = this.calculateRoundScores(roomData) || { team1: 0, team2: 0 };
      updatedPartnership.team1.score += roundScores.team1;
      updatedPartnership.team2.score += roundScores.team2;
      updatedPartnership.team1.roundScore = roundScores.team1;
      updatedPartnership.team2.roundScore = roundScores.team2;

      // Record token transfer in history
      const tokenTransferRecord: TokenTransfer = {
        fromTeam: losingTeamId,
        toTeam: winningTeamId,
        amount: tokensToTransfer,
        reason: tokenTransfer.reason,
        bidAmount,
        bidType: 'standard' // Can be enhanced to detect special bid types
      };

      const updatedTokenHistory = [...(roomData.tokenTransferHistory || []), tokenTransferRecord];

      // Record the scoring decision
      const scoringDecision: ScoringDecision = {
        decision,
        timestamp: Date.now(),
        decidedBy
      };

      // Clear scoring dialog state
      const scoringDialogState: ScoringDialogState = {
        visible: false,
        pendingDecision: false
      };

      const updatedHistory = this.logGameStateChange(
        roomData.stateHistory || [],
        decision === 'next_round' ? 'dealing' : 'in_progress',
        'scoring_decision',
        decidedBy,
        {
          decision,
          roundScores,
          tokenTransfer: tokenTransferRecord,
          gameCompletion: 'checking'
        }
      );

      if (decision === 'next_round') {
        // Check if game should end using official 304 token rules
        const gameCompletion = this.checkGameEndCondition(updatedPartnership);

        if (gameCompletion.isGameComplete) {
          // Game completed - update completion data with actual game duration
          const gameStartTime = roomData.gameFlow?.gameProgress?.gameStartTime || Date.now();
          const finalGameCompletion: GameCompletionResult = {
            ...gameCompletion,
            gameDuration: Date.now() - gameStartTime
          };

          const updatedGameFlow: GameFlow = {
            ...roomData.gameFlow,
            currentRound: {
              ...roomData.gameFlow.currentRound,
              phase: 'finished'
            },
            gameProgress: {
              ...roomData.gameFlow.gameProgress,
              roundsCompleted: roomData.gameFlow.gameProgress.roundsCompleted + 1
            }
          };

          // Log game completion
          const gameCompletionHistory = this.logGameStateChange(
            updatedHistory,
            'finished',
            'game_completed',
            'system',
            {
              winner: finalGameCompletion.winnerTeam,
              loser: finalGameCompletion.loserTeam,
              reason: finalGameCompletion.gameEndReason,
              totalRounds: finalGameCompletion.totalRoundsPlayed,
              duration: finalGameCompletion.gameDuration
            }
          );

          const updateData = {
            gameState: 'finished',
            gameFlow: updatedGameFlow,
            partnership: updatedPartnership,
            gameCompletion: finalGameCompletion,
            tokenTransferHistory: updatedTokenHistory,
            scoringDialogState: {
              visible: false,
              pendingDecision: false
            },
            lastScoringDecision: scoringDecision,
            stateHistory: gameCompletionHistory,
            updatedAt: serverTimestamp()
          };

          await updateDoc(roomRef, cleanFirestoreData(updateData));
        } else {
          // Game continues - start new round with updated token counts
          const updateData = {
            partnership: updatedPartnership,
            tokenTransferHistory: updatedTokenHistory,
            scoringDialogState: {
              visible: true, // Keep dialog visible briefly to show token transfer
              scoreValidation: roomData.scoringDialogState?.scoreValidation,
              pendingDecision: false // But mark decision as complete
            },
            lastScoringDecision: scoringDecision,
            stateHistory: updatedHistory,
            updatedAt: serverTimestamp()
          };

          await updateDoc(roomRef, cleanFirestoreData(updateData));

          // Add a 2-second delay before starting new deal to allow dialog to be seen
          setTimeout(async () => {
            try {
              // Clear the dialog and start new deal
              const roomRef = doc(db, 'gameRooms', roomId);
              await updateDoc(roomRef, {
                scoringDialogState: {
                  visible: false,
                  pendingDecision: false
                },
                updatedAt: serverTimestamp()
              });

              // Now start the new deal
              await this.progressDeal(roomId);
            } catch (error) {
              console.error('Error starting new deal after delay:', error);
            }
          }, 2000);
        }
      } else {
        // Continue current game with updated token counts
        const updateData = {
          gameState: 'in_progress',
          partnership: updatedPartnership,
          tokenTransferHistory: updatedTokenHistory,
          scoringDialogState: {
            visible: false,
            pendingDecision: false
          },
          lastScoringDecision: scoringDecision,
          stateHistory: updatedHistory,
          updatedAt: serverTimestamp()
        };

        await updateDoc(roomRef, cleanFirestoreData(updateData));
      }

    } catch (error) {
      console.error('Error processing scoring decision:', error);
      throw error;
    }
  }

  // Set up automatic timeouts for phases
  static setupPhaseTimeout(roomId: string, phase: string, timeoutMs: number): void {
    setTimeout(async () => {
      try {
        const roomDoc = await getDoc(doc(db, 'gameRooms', roomId));
        if (!roomDoc.exists()) return;
        
        const roomData = roomDoc.data() as EnhancedGameRoom;
        
        // Only trigger timeout if still in the same phase
        if (roomData.gameFlow?.currentRound?.phase === phase) {
          await this.handlePhaseTimeout(roomId, phase);
        }
      } catch (error) {
        console.error('Error handling phase timeout:', error);
      }
    }, timeoutMs);
  }

  // Handle phase timeout
  static async handlePhaseTimeout(roomId: string, phase: string): Promise<void> {
    switch (phase) {
      case 'bidding':
        // Auto-pass for current bidder
        const roomDoc = await getDoc(doc(db, 'gameRooms', roomId));
        if (roomDoc.exists()) {
          const roomData = roomDoc.data() as EnhancedGameRoom;
          await this.passBid(roomId, roomData.biddingTurn);
        }
        break;
      case 'trump_selection':
        // Auto-select first available trump card
        // Implementation would depend on available options
        break;
      case 'playing':
        // Auto-play a valid card
        // Implementation would select first valid card
        break;
    }
  }

  // Toggle auto-progress settings
  static async toggleAutoProgress(roomId: string, enabled: boolean): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;
      
      const updatedSettings = {
        ...roomData.autoProgressSettings,
        enableAutoProgress: enabled
      };

      // Log the settings change
      const updatedHistory = this.logGameStateChange(
        roomData.stateHistory || [],
        roomData.gameFlow?.currentRound?.phase || 'unknown',
        'auto_progress_toggled',
        'system',
        { enabled, previousSetting: roomData.autoProgressSettings?.enableAutoProgress }
      );

      await updateDoc(roomRef, {
        autoProgressSettings: updatedSettings,
        stateHistory: updatedHistory,
        updatedAt: serverTimestamp()
      });

      // Set up or cancel timeouts based on new setting
      if (enabled) {
        const currentPhase = roomData.gameFlow?.currentRound?.phase;
        if (currentPhase && currentPhase !== 'finished') {
          const timeoutMs = updatedSettings[`${currentPhase}Timeout` as keyof typeof updatedSettings] as number * 1000;
          this.setupPhaseTimeout(roomId, currentPhase, timeoutMs);
        }
      }
    } catch (error) {
      console.error('Error toggling auto-progress:', error);
      throw error;
    }
  }

  // Update phase timeout settings
  static async updatePhaseTimeouts(
    roomId: string, 
    timeouts: { bidTimeout?: number; playTimeout?: number; trumpSelectionTimeout?: number }
  ): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;
      
      const updatedSettings = {
        ...roomData.autoProgressSettings,
        ...timeouts
      };

      // Log the settings change
      const updatedHistory = this.logGameStateChange(
        roomData.stateHistory || [],
        roomData.gameFlow?.currentRound?.phase || 'unknown',
        'timeout_settings_updated',
        'system',
        { newTimeouts: timeouts, previousSettings: roomData.autoProgressSettings }
      );

      await updateDoc(roomRef, {
        autoProgressSettings: updatedSettings,
        stateHistory: updatedHistory,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating phase timeouts:', error);
      throw error;
    }
  }

  // Get detailed game flow statistics
  static getGameFlowStatistics(gameRoom: EnhancedGameRoom): {
    averagePhaseTime: Record<string, number>;
    totalGameTime: number;
    phaseCounts: Record<string, number>;
    dealerRotationCounts: Record<string, number>;
  } {
    const stateHistory = gameRoom.stateHistory || [];
    const gameStartTime = gameRoom.gameFlow?.gameProgress?.gameStartTime || Date.now();
    
    // Calculate phase durations
    const phaseTimes: Record<string, number[]> = {};
    const phaseCounts: Record<string, number> = {};
    const dealerRotationCounts: Record<string, number> = {};
    
    let lastPhaseStartTime = gameStartTime;
    let lastPhase = 'dealing';
    
    stateHistory.forEach(entry => {
      if (entry.action === 'phase_advance') {
        const duration = entry.timestamp - lastPhaseStartTime;
        
        if (!phaseTimes[lastPhase]) {
          phaseTimes[lastPhase] = [];
          phaseCounts[lastPhase] = 0;
        }
        
        phaseTimes[lastPhase].push(duration);
        phaseCounts[lastPhase]++;
        
        lastPhaseStartTime = entry.timestamp;
        lastPhase = entry.phase;
      }
      
      if (entry.action === 'new_deal' && entry.playerId) {
        const player = gameRoom.players.find(p => p.id === entry.playerId);
        if (player) {
          dealerRotationCounts[player.name] = (dealerRotationCounts[player.name] || 0) + 1;
        }
      }
    });
    
    // Calculate averages
    const averagePhaseTime: Record<string, number> = {};
    Object.keys(phaseTimes).forEach(phase => {
      const times = phaseTimes[phase];
      averagePhaseTime[phase] = times.reduce((sum, time) => sum + time, 0) / times.length;
    });
    
    return {
      averagePhaseTime,
      totalGameTime: Date.now() - gameStartTime,
      phaseCounts,
      dealerRotationCounts
    };
  }

  // Create a new game room
  static async createRoom(hostId: string, hostName: string): Promise<string> {
    try {
      const hostPlayer: Player = {
        id: hostId,
        name: hostName,
        position: 'bottom',
        cards: [],
        isHost: true
      };

      const gameRoom: Omit<GameRoom, 'id'> = {
        hostId,
        players: [hostPlayer],
        gameState: 'waiting',
        currentTurn: '',
        turnOrder: [],
        playedCards: [],
        round: 0,
        maxPlayers: 4,
        bids: [],
        currentBid: 0,
        biddingTurn: '',
        biddingRound: 0,
        gameScore: 200, // Standard target score for 304 game
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, 'gameRooms'), gameRoom);
      return docRef.id;
    } catch (error) {
      console.error('Error creating room:', error);
      throw error;
    }
  }

  // Join an existing game room
  static async joinRoom(roomId: string, playerId: string, playerName: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.players.length >= roomData.maxPlayers) {
        throw new Error('Room is full');
      }

      if (roomData.gameState !== 'waiting') {
        throw new Error('Game is already in progress');
      }

      // Assign position based on current players
      const positions: ('bottom' | 'right' | 'top' | 'left')[] = ['bottom', 'right', 'top', 'left'];
      const occupiedPositions = roomData.players.map(p => p.position);
      const availablePosition = positions.find(pos => !occupiedPositions.includes(pos));

      if (!availablePosition) {
        throw new Error('No available positions');
      }

      const newPlayer: Player = {
        id: playerId,
        name: playerName,
        position: availablePosition,
        cards: [],
        isHost: false
      };

      await updateDoc(roomRef, {
        players: arrayUnion(newPlayer),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error joining room:', error);
      throw error;
    }
  }

  // Start the game (only host can do this) - Enhanced with flow control
  static async startGame(roomId: string, hostId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.hostId !== hostId) {
        throw new Error('Only the host can start the game');
      }

      if (roomData.players.length < 4) {
        throw new Error('Need exactly 4 players for 304 game');
      }

      // Assign team IDs to players based on their positions
      const playersWithTeams = assignTeamIds(roomData.players);
      
      // Create partnership system
      const partnership = createPartnership(playersWithTeams);

      // Distribute initial 4 cards to players
      const { players: playersWithCards, remainingDeck } = distributeInitialCards(playersWithTeams);
      
      // Set turn order starting with bottom player
      const turnOrder = playersWithCards
        .sort((a: Player, b: Player) => {
          const positionOrder: Record<string, number> = { bottom: 0, right: 1, top: 2, left: 3 };
          return positionOrder[a.position] - positionOrder[b.position];
        })
        .map((p: Player) => p.id);

      // Initialize enhanced game flow
      const gameFlow = this.initializeGameFlow(playersWithTeams, hostId);
      const initialHistory = this.logGameStateChange([], 'dealing', 'game_started', hostId, {
        players: playersWithTeams.length,
        initialDealer: hostId
      });

      // Initialize auto-progress settings
      const autoProgressSettings = {
        enableAutoProgress: false, // Can be configured by host
        bidTimeout: 30, // 30 seconds for bidding
        playTimeout: 15, // 15 seconds for playing a card
        trumpSelectionTimeout: 20 // 20 seconds for trump selection
      };

      // Initialize deal progression settings
      const dealProgression = {
        cardsPerDeal: 8, // 304 standard
        totalDeals: 4, // Standard number of deals
        currentDeal: 1,
        dealPattern: 'standard' as const
      };

      // Start bidding phase with enhanced flow control
      await updateDoc(roomRef, {
        players: playersWithCards,
        gameState: 'bidding',
        turnOrder,
        round: 1,
        bids: [],
        currentBid: 150, // 304 rule: minimum bid is 160, so starting at 150
        biddingTurn: getBiddingStartPlayer(turnOrder, hostId), // 304 rule: start with dealer's right
        biddingRound: 1,
        biddingRestartCount: 0, // Initialize restart count
        biddingType: 'four_card', // Default to standard 304 four-card bidding
        biddingContext: {
          phase: 'four_card',
          currentBid: 150,
          hasFourCardBidCompleted: false
        },
        partnership,
        gameFlow,
        stateHistory: initialHistory,
        autoProgressSettings,
        dealProgression,
        updatedAt: serverTimestamp()
      });

      // Store the remaining deck in a separate collection for later distribution
      await addDoc(collection(db, 'remainingDecks'), {
        roomId,
        cards: remainingDeck,
        createdAt: serverTimestamp()
      });

      // Auto-advance to bidding phase after dealing delay
      setTimeout(() => {
        this.advanceGamePhase(roomId, 'bidding', hostId);
      }, 2000);

      // Set up bidding timeout if auto-progress is enabled
      if (autoProgressSettings.enableAutoProgress) {
        this.setupPhaseTimeout(roomId, 'bidding', autoProgressSettings.bidTimeout * 1000);
      }

    } catch (error) {
      console.error('Error starting game:', error);
      throw error;
    }
  }

  // Submit a bid during bidding phase (Enhanced with validation and iOS crash prevention)
  static async submitBid(roomId: string, playerId: string, bidAmount: number): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      // iOS Crash Prevention: Check if player is still in valid state
      if (!roomData.players.find(p => p.id === playerId)) {
        throw new Error('Player not found in room');
      }
      
      if (roomData.gameState !== 'bidding') {
        throw new Error('Not in bidding phase');
      }

      if (roomData.biddingTurn !== playerId) {
        throw new Error('Not your turn to bid');
      }

      // Enhanced bid validation with bidding context
      const playerBids = roomData.bids; // Pass all bids for partner validation
      const biddingContext = roomData.biddingContext || {
        phase: 'four_card',
        currentBid: roomData.currentBid,
        hasFourCardBidCompleted: false
      };
      const validation = validateBid(bidAmount, roomData.currentBid, playerBids, roomData, playerId, biddingContext);
      
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      const bid: Bid = {
        playerId,
        amount: bidAmount,
        isPassed: false,
        timestamp: Date.now(),
        bidRound: roomData.biddingRound
      };

      const newBids = [...roomData.bids, bid];
      const nextBiddingTurn = getNextBidder(playerId, roomData.turnOrder);

      // Check if bidding should continue or end
      const biddingEnds = shouldEndBidding(newBids, bidAmount, playerId, biddingContext);

      if (biddingEnds) {
        // Check if this was four card bidding and if eight card bidding should be offered
        if (biddingContext.phase === 'four_card') {
          // Store four card bidding result and offer eight card bidding
          await updateDoc(roomRef, {
            bids: newBids,
            currentBid: bidAmount,
            highestBidder: playerId,
            fourCardBids: newBids,
            gameState: 'eight_card_bidding_option', // New state for offering eight card bidding
            currentTurn: playerId, // Trumper decides on eight card bidding
            biddingContext: {
              ...biddingContext,
              fourCardBid: bidAmount,
              fourCardBidder: playerId,
              hasFourCardBidCompleted: true
            },
            updatedAt: serverTimestamp()
          });
        } else {
          // Eight card bidding is complete, move to trump selection
          await updateDoc(roomRef, {
            bids: newBids,
            currentBid: bidAmount,
            highestBidder: playerId,
            eightCardBids: newBids,
            gameState: 'trump_selection',
            currentTurn: playerId,
            updatedAt: serverTimestamp()
          });
        }
      } else {
        // Continue bidding
        await updateDoc(roomRef, {
          bids: newBids,
          currentBid: bidAmount,
          highestBidder: playerId,
          biddingTurn: nextBiddingTurn,
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Error submitting bid:', error);
      throw error;
    }
  }

  // 304 Card Rejection - Player can reject cards if total value < 15
  static async rejectCards(roomId: string, playerId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.gameState !== 'bidding') {
        throw new Error('Can only reject cards during bidding phase');
      }

      const player = roomData.players.find(p => p.id === playerId);
      if (!player) {
        throw new Error('Player not found');
      }

      // 304 rule: Can only reject before first bid is made
      if (roomData.bids.length > 0) {
        throw new Error('Cannot reject cards after bidding has started');
      }

      // Check if player has only 4 cards (initial deal)
      if (player.cards.length !== 4) {
        throw new Error('Can only reject cards with initial 4-card hand');
      }

      // Check if cards can be rejected (total value < 15)
      if (!canRejectCards(player.cards)) {
        const totalValue = player.cards.reduce((sum, card) => sum + getCardPoints(card.rank), 0);
        throw new Error(`Cannot reject cards. Total value (${totalValue}) must be less than 15`);
      }

      // Reshuffle and redeal cards
      await this.progressDeal(roomId);

    } catch (error) {
      console.error('Error rejecting cards:', error);
      throw error;
    }
  }

  // Pass during bidding phase (Enhanced with proper logic and iOS crash prevention)
  static async passBid(roomId: string, playerId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      // iOS Crash Prevention: Check if player is still in valid state
      if (!roomData.players.find(p => p.id === playerId)) {
        throw new Error('Player not found in room');
      }
      
      if (roomData.gameState !== 'bidding') {
        throw new Error('Not in bidding phase');
      }

      if (roomData.biddingTurn !== playerId) {
        throw new Error('Not your turn to bid');
      }

      const bid: Bid = {
        playerId,
        amount: 0,
        isPassed: true,
        timestamp: Date.now(),
        bidRound: roomData.biddingRound
      };

      const newBids = [...roomData.bids, bid];
      const nextBiddingTurn = getNextBidder(playerId, roomData.turnOrder);

      // Check for special case: all players pass in current round
      if (allPlayersPassedInCurrentRound(newBids, roomData.players.length, roomData.biddingRound)) {
        const restartCount = roomData.biddingRestartCount || 0;
        const maxRestarts = 2; // Maximum number of bidding restarts per deal
        
        if (restartCount < maxRestarts) {
          // Restart bidding (limited number of times)
          console.log(`All players passed in round ${roomData.biddingRound}, restarting bidding (restart ${restartCount + 1}/${maxRestarts})`);
          await updateDoc(roomRef, {
            bids: [],
            currentBid: 150, // 304 rule: reset to just below minimum (160)
            highestBidder: null,
            biddingTurn: roomData.turnOrder[0],
            biddingRound: roomData.biddingRound + 1,
            biddingRestartCount: restartCount + 1,
            updatedAt: serverTimestamp()
          });
          return;
        } else {
          // Too many restarts - end the deal and progress to next dealer
          console.log(`Maximum bidding restarts reached (${maxRestarts}), progressing to next deal`);
          await this.progressDeal(roomId);
          return;
        }
      }

      // Check if bidding should end
      const biddingContext = roomData.biddingContext || {
        phase: 'four_card',
        currentBid: roomData.currentBid,
        hasFourCardBidCompleted: false
      };
      const biddingEnds = shouldEndBidding(newBids, roomData.currentBid, roomData.highestBidder, biddingContext);

      if (biddingEnds) {
        // Bidding is complete, move to trump selection
        if (!roomData.highestBidder) {
          throw new Error('No valid bidder found');
        }
        
        await updateDoc(roomRef, {
          bids: newBids,
          gameState: 'trump_selection',
          currentTurn: roomData.highestBidder,
          updatedAt: serverTimestamp()
        });
      } else {
        // Continue bidding
        await updateDoc(roomRef, {
          bids: newBids,
          biddingTurn: nextBiddingTurn,
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Error passing bid:', error);
      throw error;
    }
  }

  // 304 Ask Partner to Bid functionality
  static async askPartnerToBid(roomId: string, requesterId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.gameState !== 'bidding') {
        throw new Error('Can only ask partner during bidding phase');
      }

      const askPartnerResult = canAskPartner(requesterId, roomData, roomData.biddingContext);
      if (!askPartnerResult.canAsk) {
        throw new Error(askPartnerResult.reason);
      }

      const askPartnerRequest: AskPartnerRequest = {
        requesterId,
        partnerId: askPartnerResult.partnerId!,
        timestamp: Date.now(),
        isProcessed: false
      };

      const updatedRequests = [...(roomData.askPartnerRequests || []), askPartnerRequest];

      // Update the bidding turn to the partner (as per 304 rules)
      await updateDoc(roomRef, {
        askPartnerRequests: updatedRequests,
        biddingTurn: askPartnerResult.partnerId,
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error asking partner to bid:', error);
      throw error;
    }
  }

  // Start Eight Card Bidding Phase (after four card bidding completes)
  static async startEightCardBidding(roomId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (!roomData.highestBidder) {
        throw new Error('No four card bid winner found');
      }

      // Distribute remaining 4 cards first
      if (!roomData.hasDistributedRemainingCards) {
        const remainingDecksQuery = collection(db, 'remainingDecks');
        const remainingDecksSnapshot = await getDocs(query(remainingDecksQuery, where('roomId', '==', roomId)));
        
        if (remainingDecksSnapshot.empty) {
          throw new Error('No remaining cards found');
        }

        const remainingDeckDoc = remainingDecksSnapshot.docs[0];
        const remainingDeck = remainingDeckDoc.data().cards;

        const updatedPlayers = distributeRemainingCards(roomData.players, remainingDeck);

        await updateDoc(roomRef, {
          players: updatedPlayers,
          hasDistributedRemainingCards: true,
          updatedAt: serverTimestamp()
        });

        // Clean up remaining deck
        await deleteDoc(remainingDeckDoc.ref);
      }

      // Set up eight card bidding context
      const eightCardBiddingContext: BiddingContext = {
        phase: 'eight_card',
        currentBid: Math.max(BIDDING_RULES.EIGHT_CARD_MIN_BID, roomData.currentBid),
        fourCardBid: roomData.currentBid,
        fourCardBidder: roomData.highestBidder,
        hasFourCardBidCompleted: true
      };

      // 304 rule: Eight card bidding starts with the four card bid winner
      await updateDoc(roomRef, {
        gameState: 'bidding',
        biddingContext: eightCardBiddingContext,
        fourCardBids: roomData.bids, // Store four card bids
        eightCardBids: [], // Reset for eight card phase
        bids: [], // Reset current bids for eight card phase
        currentBid: eightCardBiddingContext.currentBid - 10, // Set to just below minimum
        biddingTurn: roomData.highestBidder, // Trumper gets first chance
        biddingRound: 1,
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error starting eight card bidding:', error);
      throw error;
    }
  }

  // Handle trumper's decision on eight card bidding
  static async decideEightCardBidding(roomId: string, playerId: string, startEightCardBidding: boolean): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.gameState !== 'eight_card_bidding_option') {
        throw new Error('Not in eight card bidding decision phase');
      }

      if (roomData.highestBidder !== playerId) {
        throw new Error('Only the four card bid winner can decide on eight card bidding');
      }

      if (startEightCardBidding) {
        // Start eight card bidding phase
        await this.startEightCardBidding(roomId);
      } else {
        // Skip eight card bidding, go directly to trump selection
        await updateDoc(roomRef, {
          gameState: 'trump_selection',
          currentTurn: playerId,
          updatedAt: serverTimestamp()
        });
      }

    } catch (error) {
      console.error('Error deciding on eight card bidding:', error);
      throw error;
    }
  }

  // Select trump game type (enhanced with advanced mechanics)
  static async selectTrumpGameType(roomId: string, playerId: string, gameType: TrumpGameType): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.gameState !== 'trump_selection') {
        throw new Error('Not in trump selection phase');
      }

      if (roomData.highestBidder !== playerId) {
        throw new Error('Only the highest bidder can select trump game type');
      }

      // Get special rules and reveal conditions for the selected trump type
      const specialRules = getTrumpSpecialRules(gameType);
      const revealCondition = getTrumpRevealCondition(gameType);

      // Initialize trump history
      const trumpHistory = [`${playerId} selected ${gameType} trump`];

      const updateData: any = {
        trumpGameType: gameType,
        trumpSpecialRules: specialRules,
        trumpRevealCondition: revealCondition,
        trumpHistory: trumpHistory,
        trumpRevealCount: 0,
        gameState: 'trump_type_selection',
        updatedAt: serverTimestamp()
      };

      // Special handling for no trump games
      if (gameType === 'no_trump') {
        updateData.trumpSuit = null;
        updateData.isTrumpRevealed = false;
        updateData.gameState = 'in_progress'; // Skip trump card selection
        updateData.currentTurn = roomData.turnOrder[0];
        updateData.trickLeader = roomData.turnOrder[0];
      }

      await updateDoc(roomRef, updateData);
    } catch (error) {
      console.error('Error selecting trump game type:', error);
      throw error;
    }
  }

  // Select trump card after winning bid
  static async selectTrumpCard(roomId: string, playerId: string, cardId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.gameState !== 'trump_type_selection') {
        throw new Error('Not in trump card selection phase');
      }

      if (roomData.highestBidder !== playerId) {
        throw new Error('Only the highest bidder can select trump');
      }

      const player = roomData.players.find(p => p.id === playerId);
      if (!player) {
        throw new Error('Player not found');
      }

      // 304 RULE: Trump selection depends on bidding type
      const biddingType = roomData.biddingType || 'four_card'; // Default to four-card bidding
      let trumpCard: Card | undefined;
      
      if (biddingType === 'four_card') {
        // Standard 304 rule: Trump must be selected from first 4 cards only
        const initialFourCards = player.cards.slice(0, 4);
        trumpCard = initialFourCards.find(c => c.id === cardId);
        if (!trumpCard) {
          throw new Error('Trump must be selected from your first 4 cards only (304 four-card bidding rule)');
        }
      } else if (biddingType === 'eight_card') {
        // Eight-card bidding: Can select from all 8 cards
        trumpCard = player.cards.find(c => c.id === cardId);
        if (!trumpCard) {
          throw new Error('Card not found in player hand');
        }
      } else {
        throw new Error('Invalid bidding type');
      }

      // Get remaining deck from separate collection
      const remainingDecksQuery = collection(db, 'remainingDecks');
      const remainingDecksSnapshot = await getDocs(query(remainingDecksQuery, where('roomId', '==', roomId)));
      
      if (remainingDecksSnapshot.empty) {
        throw new Error('Remaining deck not found');
      }

      const remainingDeckData = remainingDecksSnapshot.docs[0].data();
      const remainingDeck = remainingDeckData.cards as Card[];

      // Check if remaining cards have already been distributed to avoid duplication
      let playersWithAllCards = roomData.players;
      if (!roomData.hasDistributedRemainingCards) {
        // Additional check: verify players don't already have 8 cards
        const currentPlayerCardCount = roomData.players[0]?.cards?.length || 0;
        if (currentPlayerCardCount === 4) {
          // Distribute remaining 4 cards to all players only if not already distributed
          playersWithAllCards = distributeRemainingCards(roomData.players, remainingDeck);
        } else if (currentPlayerCardCount === 8) {
          // Players already have 8 cards, no need to distribute
          console.log('Players already have 8 cards, skipping distribution');
        } else {
          console.warn(`Unexpected card count: ${currentPlayerCardCount}, expected 4 or 8`);
        }
      }

      // Enhanced trump revelation logic based on game type and special rules
      const revealCondition = roomData.trumpRevealCondition || getTrumpRevealCondition(roomData.trumpGameType || 'open');
      const specialRules = roomData.trumpSpecialRules || getTrumpSpecialRules(roomData.trumpGameType || 'open');
      
      const isTrumpRevealed = shouldRevealTrump(revealCondition, roomData);
      const canInspectFaceDown = specialRules.faceDownAdvantage || false;
      
      // 304 RULE: Trump suit should not be announced to other players
      const isTrumpSuitAnnounced = roomData.trumpGameType === 'open' || isTrumpRevealed;

      // Initialize progressive trump suits if needed
      const updateData: any = {
        players: playersWithAllCards,
        trumpCard: trumpCard,
        trumpSuit: trumpCard.suit,
        trumpIndicatorCardId: cardId, // Track the specific trump indicator card ID
        gameState: 'in_progress',
        currentTurn: roomData.turnOrder[0],
        trickLeader: roomData.turnOrder[0],
        isTrumpRevealed,
        isTrumpSuitAnnounced,
        canInspectFaceDown,
        hasDistributedRemainingCards: true, // Mark that remaining cards have been distributed
        updatedAt: serverTimestamp()
      };

      // Special handling for progressive trump
      if (roomData.trumpGameType === 'progressive') {
        updateData.originalTrumpSuit = trumpCard.suit;
        updateData.alternativeTrumpSuits = getProgressiveTrumpSuits(trumpCard.suit);
      }

      // Special handling for hidden trump
      if (roomData.trumpGameType === 'hidden_trump') {
        updateData.isTrumpRevealed = false; // Keep hidden until first trump is played
      }

      // Special handling for blind trump
      if (roomData.trumpGameType === 'blind_trump') {
        updateData.isTrumpRevealed = false; // Keep hidden until first trick
      }

      // Update trump history
      const trumpHistory = roomData.trumpHistory || [];
      trumpHistory.push(`Trump card selected: ${trumpCard.rank} of ${trumpCard.suit}`);
      updateData.trumpHistory = trumpHistory;

      await updateDoc(roomRef, updateData);

      // Clean up remaining deck only if we used it for card distribution
      if (!roomData.hasDistributedRemainingCards) {
        await deleteDoc(remainingDecksSnapshot.docs[0].ref);
      }
    } catch (error) {
      console.error('Error selecting trump card:', error);
      throw error;
    }
  }

  // Inspect face-down cards (only trump maker in closed trump games)
  static async inspectFaceDownCards(roomId: string, playerId: string): Promise<{ cards: PlayedCard[], hasTrumps: boolean }> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.highestBidder !== playerId) {
        throw new Error('Only the trump maker can inspect face-down cards');
      }

      if (roomData.trumpGameType !== 'closed') {
        throw new Error('Can only inspect cards in closed trump games');
      }

      if (roomData.isTrumpRevealed) {
        throw new Error('Trump is already revealed');
      }

      // Get current trick's face-down cards
      const currentTrickSize = roomData.playedCards.length % roomData.players.length;
      if (currentTrickSize !== 0) {
        throw new Error('Can only inspect face-down cards after a complete trick');
      }

      const tricksCompleted = Math.floor(roomData.playedCards.length / roomData.players.length) - 1;
      const currentTrickStart = tricksCompleted * roomData.players.length;
      const currentTrickCards = roomData.playedCards.slice(currentTrickStart, currentTrickStart + roomData.players.length);
      
      const faceDownCards = currentTrickCards.filter(card => card.isFaceDown);
      
      // Check if any face-down cards are trumps
      const hasTrumps = faceDownCards.some(card => {
        const cardData = getCardById(card.cardId);
        return cardData.suit === roomData.trumpSuit;
      });

      return { cards: faceDownCards, hasTrumps };
    } catch (error) {
      console.error('Error inspecting face-down cards:', error);
      throw error;
    }
  }

  // Reveal trump (can be called by trump maker)
  static async revealTrump(roomId: string, playerId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.highestBidder !== playerId) {
        throw new Error('Only the trump maker can reveal trump');
      }

      if (roomData.isTrumpRevealed) {
        throw new Error('Trump is already revealed');
      }

      // Reveal trump and mark all current face-down cards as revealed
      const updatedPlayedCards = roomData.playedCards.map(card => ({
        ...card,
        isRevealed: card.isFaceDown ? true : card.isRevealed
      }));

      // Update trump history and reveal count
      const trumpHistory = roomData.trumpHistory || [];
      trumpHistory.push(`Trump revealed by ${playerId}: ${roomData.trumpSuit}`);
      
      const currentRevealCount = roomData.trumpRevealCount || 0;

      await updateDoc(roomRef, {
        isTrumpRevealed: true,
        playedCards: updatedPlayedCards,
        trumpRevealCount: currentRevealCount + 1,
        trumpHistory: trumpHistory,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error revealing trump:', error);
      throw error;
    }
  }

  // Change trump suit (for maker's choice and progressive trump)
  static async changeTrumpSuit(roomId: string, playerId: string, newTrumpSuit: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      const validation = canChangeTrumpSuit(roomData, playerId, newTrumpSuit);
      if (!validation.canChange) {
        throw new Error(validation.reason);
      }

      // Update trump history
      const trumpHistory = roomData.trumpHistory || [];
      trumpHistory.push(`Trump changed from ${roomData.trumpSuit} to ${newTrumpSuit} by ${playerId}`);

      await updateDoc(roomRef, {
        trumpSuit: newTrumpSuit,
        trumpRevealCount: (roomData.trumpRevealCount || 0) + 1,
        trumpHistory: trumpHistory,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error changing trump suit:', error);
      throw error;
    }
  }

  // Auto-update progressive trump based on tricks won
  static async updateProgressiveTrump(roomId: string, trickWinner: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.trumpGameType !== 'progressive') {
        return; // Not a progressive trump game
      }

      const partnership = roomData.partnership;
      if (!partnership) return;

      const winnerTeam = getPlayerTeam(trickWinner, partnership);
      if (!winnerTeam) return;

      const tricksWon = winnerTeam.tricksWon || 0;
      const alternativeSuits = roomData.alternativeTrumpSuits || [];
      const originalTrump = roomData.originalTrumpSuit || roomData.trumpSuit;

      if (alternativeSuits.length > 0) {
        const newTrumpSuit = getNextProgressiveTrump(originalTrump!, tricksWon, alternativeSuits);
        
        if (newTrumpSuit !== roomData.trumpSuit) {
          // Trump suit changes
          const trumpHistory = roomData.trumpHistory || [];
          trumpHistory.push(`Progressive trump auto-changed to ${newTrumpSuit} (${tricksWon} tricks won)`);

          await updateDoc(roomRef, {
            trumpSuit: newTrumpSuit,
            trumpHistory: trumpHistory,
            updatedAt: serverTimestamp()
          });
        }
      }
    } catch (error) {
      console.error('Error updating progressive trump:', error);
      throw error;
    }
  }

  // Play a card
  static async playCard(roomId: string, playerId: string, cardId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as GameRoom;
      
      if (roomData.currentTurn !== playerId) {
        throw new Error('Not your turn');
      }

      // Find the player and remove the card from their hand
      const updatedPlayers = roomData.players.map(player => {
        if (player.id === playerId) {
          const cardExists = player.cards.some(card => card.id === cardId);
          if (!cardExists) {
            throw new Error('Card not in hand');
          }
          return {
            ...player,
            cards: player.cards.filter(card => card.id !== cardId)
          };
        }
        return player;
      });

      const player = roomData.players.find(p => p.id === playerId);
      if (!player) throw new Error('Player not found');

      const cardToPlay = getCardById(cardId);
      
      // Get current trick cards for validation
      const validationTrickSize = roomData.playedCards.length % roomData.players.length;
      const validationTrickStart = Math.floor(roomData.playedCards.length / roomData.players.length) * roomData.players.length;
      const validationTrickCards = roomData.playedCards.slice(validationTrickStart);
      
      // Validate suit following rules according to 304 Jass rules
      const suitValidation = this.validateSuitFollowing(
        cardToPlay,
        player.cards,
        validationTrickCards,
        roomData
      );
      
      if (!suitValidation.isValid) {
        throw new Error(suitValidation.error || 'Must follow suit if possible');
      }
      
      // Validate trump revealed scenario - when trump is revealed, standard rules apply
      const trumpRevealedValidation = validateTrumpRevealedCardPlay(
        cardId,
        roomData,
        playerId,
        validationTrickCards
      );

      if (!trumpRevealedValidation.isValid) {
        throw new Error(trumpRevealedValidation.message || 'Invalid trump card play');
      }

      // Validate trump indicator card play according to 304 rules
      const trumpIndicatorValidation = validateTrumpIndicatorInClosedGame(
        cardId,
        roomData,
        playerId,
        validationTrickCards
      );

      if (!trumpIndicatorValidation.isValid) {
        throw new Error(trumpIndicatorValidation.message || 'Invalid trump indicator card play');
      }

      // Validate trump maker restrictions in closed trump games when unable to follow suit
      const trumpMakerValidation = validateTrumpMakerClosedGameRestrictions(
        cardId,
        roomData,
        playerId,
        validationTrickCards
      );

      if (!trumpMakerValidation.isValid) {
        throw new Error(trumpMakerValidation.message || 'Invalid card play for trump maker in closed trump game');
      }

      // Validate exhausted trump rule for 250+ bids
      const isLeading = validationTrickCards.length === 0;
      const exhaustedTrumpValidation = this.validateExhaustedTrumpPlay(
        cardToPlay,
        roomData,
        playerId,
        isLeading
      );

      if (!exhaustedTrumpValidation.isValid) {
        throw new Error(exhaustedTrumpValidation.error || 'Invalid card play');
      }
      
      // Check for auto-trump reveal conditions
      let autoRevealTrump = false;
      const revealCondition = roomData.trumpRevealCondition || getTrumpRevealCondition(roomData.trumpGameType || 'open');
      
      if (!roomData.isTrumpRevealed && revealCondition === 'on_trump_play' && cardToPlay.suit === roomData.trumpSuit) {
        autoRevealTrump = true;
      }
      
      // Determine if card should be played face down
      // For trump indicator card, check if it must be played face down
      // For trump maker restrictions, check if card must be played face down
      let isFaceDown = false;
      if (trumpIndicatorValidation.mustPlayFaceDown || trumpMakerValidation.mustPlayFaceDown) {
        isFaceDown = true;
      } else {
        isFaceDown = this.shouldPlayFaceDownEnhanced(
          roomData.playedCards, 
          cardToPlay, 
          player.cards, 
          roomData.isTrumpRevealed || autoRevealTrump,
          roomData.trumpGameType,
          roomData
        );
      }

      // Add the played card
      const playedCard: PlayedCard = {
        cardId,
        playerId,
        position: player.position,
        timestamp: new Date(),
        isFaceDown,
        isRevealed: false
      };

      const newPlayedCards = [...roomData.playedCards, playedCard];
      
      // Determine next turn based on 304 rules
      let nextTurn: string;
      let updateData: any = {
        players: updatedPlayers,
        playedCards: newPlayedCards,
        updatedAt: serverTimestamp()
      };

      // Handle auto-trump reveal
      if (autoRevealTrump) {
        const trumpHistory = roomData.trumpHistory || [];
        trumpHistory.push(`Trump auto-revealed: ${roomData.trumpSuit} (${cardToPlay.rank} of ${cardToPlay.suit} played)`);
        
        updateData.isTrumpRevealed = true;
        updateData.trumpRevealCount = (roomData.trumpRevealCount || 0) + 1;
        updateData.trumpHistory = trumpHistory;
      }

      // Check if trick is complete (all players have played for this trick)
      const currentTrickSize = newPlayedCards.length % roomData.players.length;
      const isLastCardOfTrick = currentTrickSize === 0 && newPlayedCards.length > 0;
      
      if (isLastCardOfTrick) {
        // Get the cards for the current completed trick
        const tricksCompletedCount = Math.floor(newPlayedCards.length / roomData.players.length) - 1;
        const currentTrickStart = tricksCompletedCount * roomData.players.length;
        const currentTrickCards = newPlayedCards.slice(currentTrickStart, currentTrickStart + roomData.players.length);
        
        // Handle face-down card inspection in closed trump games
        let updatedPlayedCards = newPlayedCards;
        let trumpRevealed = roomData.isTrumpRevealed || false;
        let canInspectFaceDown = roomData.canInspectFaceDown || false;
        
        if (roomData.trumpGameType === 'closed' && !trumpRevealed && currentTrickCards.some(card => card.isFaceDown)) {
          // Set flag that trump maker can inspect face-down cards
          canInspectFaceDown = true;
          
          // Automatically inspect and reveal if trumps are found
          const faceDownCards = currentTrickCards.filter(card => card.isFaceDown);
          const hasTrumps = faceDownCards.some(card => {
            const cardData = getCardById(card.cardId);
            return cardData.suit === roomData.trumpSuit;
          });
          
          if (hasTrumps) {
            // Automatically reveal trump and mark all cards in this trick as revealed
            trumpRevealed = true;
            canInspectFaceDown = false;
            updatedPlayedCards = newPlayedCards.map(card => {
              if (currentTrickCards.some(trickCard => trickCard.cardId === card.cardId)) {
                return { ...card, isRevealed: true };
              }
              return card;
            });
          }
        }
        
        // 304 RULE: For 250+ bids, auto-reveal trump after first trick
        let autoRevealAfterFirstTrick = false;
        const completedTricks = Math.floor(newPlayedCards.length / roomData.players.length);
        if (completedTricks === 1) { // First trick just completed
          const specialRules = this.get304SpecialBidRules(roomData.currentBid);
          if (specialRules.autoRevealAfterFirstTrick && !trumpRevealed) {
            autoRevealAfterFirstTrick = true;
            trumpRevealed = true;
            canInspectFaceDown = false;
            
            // Reveal all cards in this trick
            updatedPlayedCards = newPlayedCards.map(card => {
              if (currentTrickCards.some(trickCard => trickCard.cardId === card.cardId)) {
                return { ...card, isRevealed: true };
              }
              return card;
            });
          }
        }

        // Trick is complete - determine winner according to 304 rules
        const trickWinner = determineTrickWinner(currentTrickCards, roomData.trumpSuit);
        
        // Update tricks won count for individual players
        const playersWithTricks = updatedPlayers.map(player => {
          if (player.id === trickWinner) {
            return {
              ...player,
              tricksWon: (player.tricksWon || 0) + 1
            };
          }
          return player;
        });

        // Update team tricks won count
        let updatedPartnership = roomData.partnership;
        if (updatedPartnership) {
          const winnerTeam = getPlayerTeam(trickWinner, updatedPartnership);
          if (winnerTeam) {
            updatedPartnership = {
              ...updatedPartnership,
              [winnerTeam.id]: {
                ...winnerTeam,
                tricksWon: winnerTeam.tricksWon + 1
              }
            };
          }
        }
        
        // Trick winner leads next trick
        nextTurn = trickWinner;
        
        updateData = {
          ...updateData,
          players: playersWithTricks,
          playedCards: updatedPlayedCards,
          currentTurn: nextTurn,
          lastTrickWinner: trickWinner,
          trickLeader: trickWinner,
          isTrumpRevealed: trumpRevealed,
          canInspectFaceDown,
          partnership: updatedPartnership
        };

        // Add trump history if auto-revealed after first trick
        if (autoRevealAfterFirstTrick) {
          const trumpHistory = roomData.trumpHistory || [];
          trumpHistory.push(`250+ Bid Rule: Trump auto-revealed after first trick (${roomData.trumpSuit})`);
          updateData.trumpHistory = trumpHistory;
          updateData.trumpRevealCount = (roomData.trumpRevealCount || 0) + 1;
        }
        
        // Handle progressive trump update
        if (roomData.trumpGameType === 'progressive') {
          await this.updateProgressiveTrump(roomId, trickWinner);
        }

        // Wait 3 seconds then clear the played cards
        setTimeout(async () => {
          await this.clearPlayedCardsAndSetNextTrick(roomId, trickWinner);
        }, 3000);
      } else {
        // Trick not complete - next player in turn order plays
        const currentTurnIndex = roomData.turnOrder.indexOf(playerId);
        const nextTurnIndex = (currentTurnIndex + 1) % roomData.turnOrder.length;
        nextTurn = roomData.turnOrder[nextTurnIndex];
        
        updateData.currentTurn = nextTurn;
      }

      await updateDoc(roomRef, updateData);
    } catch (error) {
      console.error('Error playing card:', error);
      throw error;
    }
  }

  // Clear played cards (after each round)
  static async clearPlayedCards(roomId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      await updateDoc(roomRef, {
        playedCards: [],
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error clearing played cards:', error);
      throw error;
    }
  }

  // Clear played cards and set up next trick (304 specific) - Enhanced with flow control
  static async clearPlayedCardsAndSetNextTrick(roomId: string, trickWinner: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        throw new Error('Room not found');
      }

      const roomData = roomDoc.data() as EnhancedGameRoom;
      
      // Check if game should end (all cards played)
      const remainingCards = roomData.players.some(player => player.cards.length > 0);
      
      if (!remainingCards) {
        // Round is complete - handle scoring and progression
        await this.completeRound(roomId);
      } else {
        // Update trick count and continue playing
        const updatedGameFlow: GameFlow = {
          ...roomData.gameFlow,
          currentRound: {
            ...roomData.gameFlow.currentRound,
            tricksCompleted: roomData.gameFlow.currentRound.tricksCompleted + 1
          }
        };

        // Log trick completion
        const updatedHistory = this.logGameStateChange(
          roomData.stateHistory || [],
          'playing',
          'trick_completed',
          trickWinner,
          { 
            trickNumber: updatedGameFlow.currentRound.tricksCompleted,
            remainingCards: roomData.players.reduce((sum, p) => sum + p.cards.length, 0)
          }
        );

        // Clear cards and set trick winner as next leader
        const updateData = {
          playedCards: [],
          currentTurn: trickWinner,
          trickLeader: trickWinner,
          gameFlow: updatedGameFlow,
          stateHistory: updatedHistory,
          updatedAt: serverTimestamp()
        };

        await updateDoc(roomRef, cleanFirestoreData(updateData));

        // Set up timeout for next card play if auto-progress is enabled
        if (roomData.autoProgressSettings?.enableAutoProgress) {
          this.setupPhaseTimeout(roomId, 'playing', roomData.autoProgressSettings.playTimeout * 1000);
        }
      }
    } catch (error) {
      console.error('Error clearing played cards and setting next trick:', error);
      throw error;
    }
  }

  // Leave room
  static async leaveRoom(roomId: string, playerId: string): Promise<void> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (!roomDoc.exists()) {
        return; // Room doesn't exist, nothing to do
      }

      const roomData = roomDoc.data() as GameRoom;
      const playerToRemove = roomData.players.find(p => p.id === playerId);
      
      if (!playerToRemove) {
        return; // Player not in room
      }

      // If this is the host leaving, either promote another player or delete room
      if (playerToRemove.isHost && roomData.players.length > 1) {
        const newHost = roomData.players.find(p => p.id !== playerId);
        if (newHost) {
          const updatedPlayers = roomData.players
            .filter(p => p.id !== playerId)
            .map(p => p.id === newHost.id ? { ...p, isHost: true } : p);
          
          await updateDoc(roomRef, {
            hostId: newHost.id,
            players: updatedPlayers,
            updatedAt: serverTimestamp()
          });
        }
      } else if (roomData.players.length === 1) {
        // Last player leaving, delete the room
        await deleteDoc(roomRef);
      } else {
        // Regular player leaving
        await updateDoc(roomRef, {
          players: arrayRemove(playerToRemove),
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Error leaving room:', error);
      throw error;
    }
  }

  // Subscribe to room updates
  static subscribeToRoom(roomId: string, callback: (room: GameRoom | null) => void): () => void {
    const roomRef = doc(db, 'gameRooms', roomId);
    
    return onSnapshot(roomRef, (doc) => {
      if (doc.exists()) {
        callback({ id: doc.id, ...doc.data() } as GameRoom);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error listening to room updates:', error);
      callback(null);
    });
  }

  // Get room data once
  static async getRoom(roomId: string): Promise<GameRoom | null> {
    try {
      const roomRef = doc(db, 'gameRooms', roomId);
      const roomDoc = await getDoc(roomRef);
      
      if (roomDoc.exists()) {
        return { id: roomDoc.id, ...roomDoc.data() } as GameRoom;
      }
      return null;
    } catch (error) {
      console.error('Error getting room:', error);
      return null;
    }
  }

  // Get all available rooms
  static async getAvailableRooms(): Promise<GameRoom[]> {
    try {
      const roomsCollection = collection(db, 'gameRooms');
      const roomsQuery = query(
        roomsCollection,
        where('gameState', '==', 'waiting')
      );
      const roomsSnapshot = await getDocs(roomsQuery);
      
      const rooms: GameRoom[] = [];
      roomsSnapshot.forEach((doc) => {
        const data = doc.data() as GameRoom;
        rooms.push({ ...data, id: doc.id });
      });
      
      // Sort by creation time (newest first)
      return rooms.sort((a, b) => {
        if (a.createdAt && b.createdAt) {
          return b.createdAt.seconds - a.createdAt.seconds;
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting available rooms:', error);
      throw error;
    }
  }

  // Subscribe to available rooms changes
  static subscribeToAvailableRooms(callback: (rooms: GameRoom[]) => void): () => void {
    const roomsCollection = collection(db, 'gameRooms');
    const roomsQuery = query(
      roomsCollection,
      where('gameState', '==', 'waiting')
    );
    
    return onSnapshot(roomsQuery, (snapshot) => {
      const rooms: GameRoom[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data() as GameRoom;
        rooms.push({ ...data, id: doc.id });
      });
      
      // Sort by creation time (newest first)
      const sortedRooms = rooms.sort((a, b) => {
        if (a.createdAt && b.createdAt) {
          return b.createdAt.seconds - a.createdAt.seconds;
        }
        return 0;
      });
      
      callback(sortedRooms);
    }, (error) => {
      console.error('Error listening to available rooms:', error);
      callback([]);
    });
  }

  // Enhanced 304 Special Rules for 250+ bids
  static get304SpecialBidRules(finalBid: number): SpecialBidRules {
    const is250Plus = finalBid >= BIDDING_RULES.HIGH_BID_THRESHOLD;
    
    return {
      is250Plus,
      autoRevealAfterFirstTrick: is250Plus,
      exhaustedTrumpEnforced: is250Plus,
      communicationRestricted: true, // Always restricted in 304
      faceDownInFirstTrickOnly: is250Plus,
    };
  }

  // Check if trump maker has exhausted trumps (all remaining trumps in their control)
  static checkExhaustedTrumps(
    gameRoom: GameRoom,
    trumpMaker: Player
  ): { isExhausted: boolean; remainingTrumps: Card[]; mustLeadAll: boolean } {
    if (!gameRoom.trumpSuit) {
      return { isExhausted: false, remainingTrumps: [], mustLeadAll: false };
    }

    // Get all trump cards remaining in trump maker's hand (excluding trump indicator card)
    const trumpsInHand = trumpMaker.cards.filter(card => 
      card.suit === gameRoom.trumpSuit && card.id !== gameRoom.trumpIndicatorCardId
    );

    // Count trump cards still in play (not yet played)
    const playedTrumpCards = gameRoom.playedCards.filter(playedCard => {
      const card = getCardById(playedCard.cardId);
      return card.suit === gameRoom.trumpSuit;
    });

    // Total trump cards in deck (8 total: J, 9, A, 10, K, Q, 8, 7 of trump suit)
    const totalTrumpsInDeck = 8;
    const trumpsStillInPlay = totalTrumpsInDeck - playedTrumpCards.length;

    // Account for trump indicator card if still unplayed
    let trumpIndicatorStillAvailable = false;
    if (gameRoom.trumpIndicatorCardId) {
      const trumpIndicatorWasPlayed = gameRoom.playedCards.some(pc => 
        pc.cardId === gameRoom.trumpIndicatorCardId
      );
      trumpIndicatorStillAvailable = !trumpIndicatorWasPlayed;
    }

    const trumpsInTrumpMakerControl = trumpsInHand.length + (trumpIndicatorStillAvailable ? 1 : 0);

    // Exhausted trumps: trump maker has ALL remaining trump cards
    const isExhausted = trumpsInTrumpMakerControl === trumpsStillInPlay && trumpsInHand.length > 0;

    // Must lead all trumps when exhausted (304 rule)
    const specialRules = this.get304SpecialBidRules(gameRoom.currentBid);
    const mustLeadAll = isExhausted && specialRules.exhaustedTrumpEnforced;

    return {
      isExhausted,
      remainingTrumps: trumpsInHand,
      mustLeadAll
    };
  }

  // Validate card play according to 304 Exhausted Trump Rule
  static validateExhaustedTrumpPlay(
    cardToPlay: Card,
    gameRoom: GameRoom,
    playerId: string,
    isLeading: boolean
  ): { isValid: boolean; error?: string; mustPlayTrump?: boolean } {
    if (gameRoom.highestBidder !== playerId || !isLeading) {
      return { isValid: true }; // Rule only applies to trump maker when leading
    }

    const trumpMaker = gameRoom.players.find(p => p.id === playerId);
    if (!trumpMaker) {
      return { isValid: true };
    }

    const exhaustedCheck = this.checkExhaustedTrumps(gameRoom, trumpMaker);
    
    if (!exhaustedCheck.isExhausted || !exhaustedCheck.mustLeadAll) {
      return { isValid: true }; // No exhausted trump constraint
    }

    // 304 RULE: When trumps are exhausted, trump maker MUST lead all remaining trumps
    const isPlayingTrump = cardToPlay.suit === gameRoom.trumpSuit && 
                           cardToPlay.id !== gameRoom.trumpIndicatorCardId;

    if (!isPlayingTrump && exhaustedCheck.remainingTrumps.length > 0) {
      return { 
        isValid: false, 
        error: `Exhausted Trump Rule: You must lead all remaining trump cards first (${exhaustedCheck.remainingTrumps.length} remaining)`,
        mustPlayTrump: true 
      };
    }

    return { isValid: true };
  }

  // Enhanced face-down card logic for 250+ bids
  static shouldPlayFaceDownEnhanced(
    playedCards: PlayedCard[],
    cardToPlay: Card,
    playerCards: Card[],
    isTrumpRevealed: boolean,
    trumpGameType?: TrumpGameType,
    gameRoom?: GameRoom
  ): boolean {
    // Original face-down logic with gameRoom parameter
    const originalLogic = shouldPlayFaceDown(
      playedCards,
      cardToPlay,
      playerCards,
      isTrumpRevealed,
      trumpGameType,
      gameRoom
    );

    // Enhanced logic for 250+ bids
    if (gameRoom) {
      const specialRules = this.get304SpecialBidRules(gameRoom.currentBid);
      
      if (specialRules.faceDownInFirstTrickOnly) {
        // 304 RULE: For 250+ bids, face-down cards only in first trick
        const tricksCompleted = Math.floor(playedCards.length / gameRoom.players.length);
        
        if (tricksCompleted > 0) {
          // After first trick, all cards played face-up for 250+ bids
          return false;
        }
      }
    }

    return originalLogic;
  }

  // Communication restriction enforcement (UI-level implementation hint)
  static getCommunicationRestrictions(gameRoom: GameRoom): {
    allowChat: boolean;
    allowEmotes: boolean;
    allowedPhrases: string[];
    restrictionReason: string;
  } {
    const specialRules = this.get304SpecialBidRules(gameRoom.currentBid);
    
    if (specialRules.communicationRestricted && gameRoom.gameState === 'in_progress') {
      return {
        allowChat: false,
        allowEmotes: false,
        allowedPhrases: [
          "Pass",
          "Bid",
          "Good game",
          "Thanks"
        ],
        restrictionReason: "304 Rule: No verbal or gestural communication allowed during card play"
      };
    }

    return {
      allowChat: true,
      allowEmotes: true,
      allowedPhrases: [],
      restrictionReason: ""
    };
  }

  /**
   * Validates suit following rules according to 304 Jass rules
   * Players must follow suit (play a card of the same suit that was led) if they can.
   * If unable to follow suit, they may play any card.
   */
  static validateSuitFollowing(
    cardToPlay: Card,
    playerCards: Card[],
    currentTrickCards: PlayedCard[],
    gameRoom: GameRoom
  ): { isValid: boolean; error?: string } {
    // If this is the first card of the trick (leading), any card is allowed
    if (currentTrickCards.length === 0) {
      return { isValid: true };
    }

    // Get the suit that was led (first card of the trick)
    const leadCard = currentTrickCards[0];
    const leadCardData = getCardById(leadCard.cardId);
    const ledSuit = leadCardData.suit;

    // Check if the player is trying to play a card of the led suit
    const isFollowingSuit = cardToPlay.suit === ledSuit;

    // If the player is following suit, it's always valid
    if (isFollowingSuit) {
      return { isValid: true };
    }

    // If not following suit, check if the player has any cards of the led suit
    // Include the card they're trying to play in the check since it hasn't been removed yet
    const allPlayerCards = [...playerCards, cardToPlay];
    let cardsOfLedSuit = allPlayerCards.filter(card => card.suit === ledSuit);

    // 304 SPECIAL RULE: Trump indicator card restrictions only apply in closed trump games
    // When trump is revealed, all cards follow standard trick-taking rules
    if (gameRoom.trumpGameType === 'closed' &&
        !gameRoom.isTrumpRevealed &&
        ledSuit === gameRoom.trumpSuit &&
        gameRoom.trumpIndicatorCardId) {

      // In closed trump games, trump indicator card cannot be used to follow trump suit
      cardsOfLedSuit = cardsOfLedSuit.filter(card => card.id !== gameRoom.trumpIndicatorCardId);
      console.log('🎯 Server: Trump led in closed game - excluding trump indicator from follow suit check');
      console.log('🎯 Server: Effective cards of led suit after excluding trump indicator:', cardsOfLedSuit.length);
    }

    // 304 RULE: When trump is revealed, all trump cards can be played normally
    // No special restrictions apply except for the trump indicator card's specific rules
    if (gameRoom.isTrumpRevealed && ledSuit === gameRoom.trumpSuit) {
      // Trump is revealed - all trump cards can follow suit normally
      // Only exclude trump indicator if it has specific play restrictions (handled elsewhere)
      console.log('🎯 Server: Trump revealed - all trump cards can follow suit normally');
      console.log('🎯 Server: Player has', cardsOfLedSuit.length, 'trump cards to follow suit');
      console.log('🎯 Server: Card being played:', cardToPlay.suit, cardToPlay.rank);
    }

    // If the player has cards of the led suit (after exclusions), they must play one of them
    if (cardsOfLedSuit.length > 0) {
      return {
        isValid: false,
        error: `You must follow suit. You have ${cardsOfLedSuit.length} card(s) of ${ledSuit} that you must play.`
      };
    }

    // If the player has no cards of the led suit (or only excluded cards), they can play any card
    // This includes playing trump cards to cut when unable to follow suit
    return { isValid: true };
  }
} 