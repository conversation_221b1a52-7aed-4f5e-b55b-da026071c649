import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { Partnership, TokenTransfer } from '../services/gameService';

interface TokenScoreDisplayProps {
  partnership: Partnership;
  recentTokenTransfer?: TokenTransfer;
  showDetails?: boolean;
  onToggleDetails?: () => void;
}

export default function TokenScoreDisplay({
  partnership,
  recentTokenTransfer,
  showDetails = false,
  onToggleDetails
}: TokenScoreDisplayProps) {

  const getWinStreakDisplay = (consecutiveWins: number): string => {
    if (consecutiveWins === 0) return '';
    if (consecutiveWins === 1) return '🔥';
    if (consecutiveWins === 2) return '🔥🔥';
    if (consecutiveWins >= 3) return '🔥🔥🔥';
    return '';
  };

  const getTokenTransferIcon = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return '✅';
      case 'failed_bid':
        return '❌';
      case 'caps_bonus':
        return '🎯';
      case 'wrong_caps_penalty':
        return '⚠️';
      case 'caps_failure_penalty':
        return '💥';
      default:
        return '🔄';
    }
  };

  const getTokenTransferText = (reason: TokenTransfer['reason']): string => {
    switch (reason) {
      case 'successful_bid':
        return 'Bid Made';
      case 'failed_bid':
        return 'Bid Failed';
      case 'caps_bonus':
        return 'Caps Bonus';
      case 'wrong_caps_penalty':
        return 'Wrong Caps';
      case 'caps_failure_penalty':
        return 'Caps Failed';
      default:
        return 'Transfer';
    }
  };

  return (
    <View className="bg-gray-800 rounded-lg p-4 border border-gray-600">
      {/* Header */}
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-lg font-bold text-white">
          Official 304 Scoring
        </Text>
        {onToggleDetails && (
          <TouchableOpacity
            onPress={onToggleDetails}
            className="bg-blue-600 px-3 py-1 rounded"
          >
            <Text className="text-white text-sm">
              {showDetails ? 'Hide' : 'Details'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Token Counts */}
      <View className="flex-row justify-between mb-4">
        {/* Team 1 */}
        <View className="flex-1 bg-blue-900/30 rounded-lg p-3 mr-2 border border-blue-500">
          <Text className="text-blue-300 font-semibold text-center mb-1">
            {partnership.team1.name}
          </Text>
          <View className="items-center">
            <Text className="text-2xl font-bold text-white">
              {partnership.team1.tokens}
            </Text>
            <Text className="text-xs text-blue-200">tokens</Text>
            {partnership.team1.consecutiveWins > 0 && (
              <View className="flex-row items-center mt-1">
                <Text className="text-xs text-yellow-400 mr-1">
                  {getWinStreakDisplay(partnership.team1.consecutiveWins)}
                </Text>
                <Text className="text-xs text-yellow-400">
                  {partnership.team1.consecutiveWins} wins
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* VS Divider */}
        <View className="justify-center items-center px-2">
          <Text className="text-gray-400 font-bold">VS</Text>
          <Text className="text-xs text-gray-500">22 total</Text>
        </View>

        {/* Team 2 */}
        <View className="flex-1 bg-red-900/30 rounded-lg p-3 ml-2 border border-red-500">
          <Text className="text-red-300 font-semibold text-center mb-1">
            {partnership.team2.name}
          </Text>
          <View className="items-center">
            <Text className="text-2xl font-bold text-white">
              {partnership.team2.tokens}
            </Text>
            <Text className="text-xs text-red-200">tokens</Text>
            {partnership.team2.consecutiveWins > 0 && (
              <View className="flex-row items-center mt-1">
                <Text className="text-xs text-yellow-400 mr-1">
                  {getWinStreakDisplay(partnership.team2.consecutiveWins)}
                </Text>
                <Text className="text-xs text-yellow-400">
                  {partnership.team2.consecutiveWins} wins
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Recent Token Transfer */}
      {recentTokenTransfer && (
        <View className="bg-yellow-900/20 rounded-lg p-3 mb-3 border border-yellow-600">
          <Text className="text-yellow-300 font-semibold text-center mb-2">
            Latest Token Transfer
          </Text>
          <View className="flex-row items-center justify-center">
            <Text className="text-lg mr-2">
              {getTokenTransferIcon(recentTokenTransfer.reason)}
            </Text>
            <Text className="text-white text-sm">
              {recentTokenTransfer.amount} tokens • {getTokenTransferText(recentTokenTransfer.reason)}
            </Text>
          </View>
          {recentTokenTransfer.bidAmount && (
            <Text className="text-yellow-200 text-xs text-center mt-1">
              Bid: {recentTokenTransfer.bidAmount}
            </Text>
          )}
        </View>
      )}

      {/* Detailed Statistics */}
      {showDetails && (
        <View className="bg-gray-700 rounded-lg p-3 space-y-2">
          <Text className="text-white font-semibold text-center mb-2">
            Detailed Statistics
          </Text>
          
          <View className="flex-row justify-between">
            <Text className="text-gray-300 text-sm">Team 1 Total Wins:</Text>
            <Text className="text-white text-sm font-semibold">
              {partnership.team1.totalRoundsWon}
            </Text>
          </View>
          
          <View className="flex-row justify-between">
            <Text className="text-gray-300 text-sm">Team 2 Total Wins:</Text>
            <Text className="text-white text-sm font-semibold">
              {partnership.team2.totalRoundsWon}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-gray-300 text-sm">Legacy Score (Team 1):</Text>
            <Text className="text-white text-sm">
              {partnership.team1.score}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <Text className="text-gray-300 text-sm">Legacy Score (Team 2):</Text>
            <Text className="text-white text-sm">
              {partnership.team2.score}
            </Text>
          </View>
        </View>
      )}

      {/* Progress Bar */}
      <View className="mt-3">
        <View className="bg-gray-600 h-3 rounded-full overflow-hidden">
          <View 
            className="bg-blue-500 h-full"
            style={{ 
              width: `${(partnership.team1.tokens / 22) * 100}%` 
            }}
          />
        </View>
        <View className="flex-row justify-between mt-1">
          <Text className="text-xs text-blue-300">
            {partnership.team1.name}: {Math.round((partnership.team1.tokens / 22) * 100)}%
          </Text>
          <Text className="text-xs text-red-300">
            {partnership.team2.name}: {Math.round((partnership.team2.tokens / 22) * 100)}%
          </Text>
        </View>
      </View>
    </View>
  );
}
