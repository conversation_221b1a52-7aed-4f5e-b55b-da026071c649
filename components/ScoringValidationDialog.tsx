import React, { useEffect, useState } from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScoreValidationResult } from '../services/gameService';

const { width, height } = Dimensions.get('window');

interface ScoringValidationDialogProps {
  visible: boolean;
  scoreValidation: ScoreValidationResult;
  onAutoNextRound: () => void;
  pendingDecision?: boolean; // Whether a decision is still pending
}

export default function ScoringValidationDialog({
  visible,
  scoreValidation,
  onAutoNextRound,
  pendingDecision = true
}: ScoringValidationDialogProps) {
  const [countdown, setCountdown] = useState(10);

  console.log('ScoringValidationDialog render - visible:', visible, 'pendingDecision:', pendingDecision);

  // Auto-progression timer
  useEffect(() => {
    if (!visible || !pendingDecision) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Time's up - automatically proceed to next round
          onAutoNextRound();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, pendingDecision, onAutoNextRound]);

  // Reset countdown when dialog becomes visible
  useEffect(() => {
    if (visible && pendingDecision) {
      setCountdown(10);
    }
  }, [visible, pendingDecision]);

  // Don't render if not visible
  if (!visible) {
    return null;
  }

  const { bidderTeam, bidAmount, bidderTeamScore, excessPoints } = scoreValidation;
  const isDecisionMade = !pendingDecision;

  return (
    <SafeAreaView style={styles.modalOverlay}>
      <View style={styles.modalContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <View style={styles.headerBadge}>
            <Text style={styles.headerBadgeText}>🎯 Scoring Validation</Text>
          </View>
          {pendingDecision && (
            <View style={styles.countdownContainer}>
              <Text style={styles.countdownText}>
                ⏱️ Automatically starting next round in {countdown} seconds
              </Text>
            </View>
          )}
        </View>

        {/* Score Information */}
        <View style={styles.scoreInfoContainer}>
          <View style={styles.scoreCard}>
            <Text style={styles.scoreCardTitle}>Bid Results</Text>
            <View style={styles.scoreRow}>
              <Text style={styles.scoreLabel}>Team:</Text>
              <Text style={styles.scoreValue}>{bidderTeam.name}</Text>
            </View>
            <View style={styles.scoreRow}>
              <Text style={styles.scoreLabel}>Bid Amount:</Text>
              <Text style={styles.scoreValue}>{bidAmount} points</Text>
            </View>
            <View style={styles.scoreRow}>
              <Text style={styles.scoreLabel}>Actual Score:</Text>
              <Text style={[styles.scoreValue, styles.successText]}>{bidderTeamScore} points</Text>
            </View>
            <View style={styles.scoreRow}>
              <Text style={styles.scoreLabel}>Excess Points:</Text>
              <Text style={[styles.scoreValue, styles.bonusText]}>+{excessPoints} points</Text>
            </View>
          </View>

          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>
              ✅ Bid Successful! The bidding team exceeded their bid by {excessPoints} points.
            </Text>
            <Text style={styles.instructionText}>
              {isDecisionMade
                ? "🔄 Starting next round..."
                : "The next round will start automatically in a few seconds."
              }
            </Text>
          </View>
        </View>



        {/* Footer */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            According to 304 rules, when a bid is exceeded, the game automatically proceeds to the next round.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: '#1f2937',
    borderRadius: 20,
    padding: 24,
    width: Math.min(width - 40, 400),
    maxHeight: height - 100,
    borderWidth: 2,
    borderColor: '#374151',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerBadge: {
    backgroundColor: '#059669',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 12,
  },
  headerBadgeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  countdownContainer: {
    backgroundColor: '#1f2937',
    borderRadius: 12,
    padding: 12,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#374151',
  },
  countdownText: {
    color: '#fbbf24',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  scoreInfoContainer: {
    marginBottom: 24,
  },
  scoreCard: {
    backgroundColor: '#374151',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  scoreCardTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  scoreRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scoreLabel: {
    color: '#9ca3af',
    fontSize: 14,
  },
  scoreValue: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  successText: {
    color: '#10b981',
  },
  bonusText: {
    color: '#f59e0b',
  },
  statusContainer: {
    backgroundColor: '#065f46',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#10b981',
  },
  statusText: {
    color: '#d1fae5',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  instructionText: {
    color: '#a7f3d0',
    fontSize: 12,
  },

  footerContainer: {
    borderTopWidth: 1,
    borderTopColor: '#374151',
    paddingTop: 16,
  },
  footerText: {
    color: '#9ca3af',
    fontSize: 11,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
